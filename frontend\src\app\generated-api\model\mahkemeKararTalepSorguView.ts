/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface MahkemeKararTalepSorguView { 
    mahkemeKararTalepId?: number;
    evrakId?: number;
    evrakSiraNo?: string;
    evrakNo?: string;
    kurumEvrakNo?: string;
    kurumEvrakTarihi?: string;
    kararKayitTarihi?: string;
    mahkemeIlIlceKodu?: string;
    mahkemeIlIlceAdi?: string;
    kaydedenKullaniciId?: number;
    kaydedenKullaniciAdi?: string;
    kaydedenAdiSoyadi?: string;
    adi?: string;
    soyadi?: string;
    sorusturmaNo?: string;
    mahkemeKararNo?: string;
    aciklama?: string;
    durumu?: string;
    mahkemeKodu?: string;
    mahkemeAdi?: string;
    kurumKodu?: string;
    kurumAdi?: string;
    evrakKonusu?: string;
}

