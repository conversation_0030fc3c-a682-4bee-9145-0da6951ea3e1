package iym.makos.domain.mktalep.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.talep.*;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mktalep.*;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDSonlandirmaKararDetay;
import iym.makos.model.dto.view.info.*;
import iym.makos.model.dto.view.mapper.HedeflerIslemEnricherMapper;
import iym.makos.model.dto.view.mapper.KararAidiyatIslemEnricherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class IDSonlandirmaTalepEnricher implements MKTalepEnricher {

    private final DbHedeflerTalepService dbHedeflerIslemService;
    private final HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper;
    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatService;
    private final DbMahkemeAidiyatTalepService dbMahkemeAidiyatService;
    private final KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper;
    private final DbMahkemeSuclarTalepService dbMahkemeSuclarService;
    private final DbSucTipiService dbSucTipiService;

    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    private final DbHedeflerDetayTalepService dbHedeflerDetayService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararService;

    @Autowired
    public IDSonlandirmaTalepEnricher(
            DbHedeflerTalepService dbHedeflerIslemService
            , HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper
            , DbHedeflerAidiyatTalepService dbHedeflerAidiyatService
            , DbMahkemeAidiyatTalepService dbMahkemeAidiyatService
            , KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper
            , DbMahkemeSuclarTalepService dbMahkemeSuclarService
            , DbSucTipiService dbSucTipiService
            , DbHedeflerDetayTalepService dbHedeflerDetayService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService

    ) {
        this.dbHedeflerIslemService = dbHedeflerIslemService;
        this.hedeflerIslemEnricherMapper = hedeflerIslemEnricherMapper;
        this.dbHedeflerAidiyatService = dbHedeflerAidiyatService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;
        this.iDKararAidiyatIslemEnricherMapper = iDKararAidiyatIslemEnricherMapper;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbSucTipiService = dbSucTipiService;
        this.dbHedeflerDetayService = dbHedeflerDetayService;
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {
        try {
            Long mahkemeKararTalepId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararTalepId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararTalepId yok");
            }

            //enrich only IDYeniKararDetay

            //Kararin Hedefler Listesi
            List<HedeflerTalep> hedeflerList = dbHedeflerIslemService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            List<SonlandirmaKarariHedefInfo> sonlandirmaKarariHedefList = enrichHedefler(hedeflerList);

            //Kararin Aidiyat Listsi
            List<MahkemeAidiyatTalep> aidiyatListesi = dbMahkemeAidiyatService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            List<IDKarariAidiyatInfo> iDKarariAidiyatInfoList = enrichAidiyatListesi(aidiyatListesi);

            //Kararin Suc Tipleri
            List<MahkemeSuclarTalep> sucLitesi = dbMahkemeSuclarService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            List<IDKarariSucTipiInfo> sucTipleriDTOList = enrichSucTipleri(sucLitesi, mahkemeKararTalepId);

            //IDMahkemeKarariInfo'in enrich edilecek nesnesi : MkIslemIDYeniKararDTO
            IDSonlandirmaKararDetay sonlandirmaKararDetay = IDSonlandirmaKararDetay.builder()
                    .hedefListesi(sonlandirmaKarariHedefList)
                    .aidiyatListesi(iDKarariAidiyatInfoList)
                    .sucTipleri(sucTipleriDTOList)
                    .build();
            mahkemeKararIslem.setIDSonlandirmaKararDetay(sonlandirmaKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("IDSonlandirmaTalepEnricher failed. mahkemeKararTalepId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }

    private List<IDKarariSucTipiInfo> enrichSucTipleri(List<MahkemeSuclarTalep> kararSucTipleri, Long mahkemeKararTalepId) {
        List<IDKarariSucTipiInfo> result = new ArrayList<>();
        List<SucTipi> sucTipiListesi = dbSucTipiService.getByMahkemeTalepId(mahkemeKararTalepId);
        CommonUtils.safeList(kararSucTipleri).forEach(mahkemeKararSucTipi -> {

            SucTipi sucTipi = sucTipiListesi.stream()
                    .filter(s -> s.getSucTipiKodu().equals(mahkemeKararSucTipi.getSucTipKodu()))
                    .findFirst()
                    .orElse(null);

            SucTipiInfo sucTipiInfo = SucTipiInfo.builder()
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipAdi(sucTipi != null ? sucTipi.getAciklama() : "")
                    .build();

            IDKarariSucTipiInfo iDKarariSucTipiInfo = IDKarariSucTipiInfo.builder()
                    .id(mahkemeKararSucTipi.getId())
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipi(sucTipiInfo)
                    .mahkemeKararId(mahkemeKararTalepId)
                    .build();

            result.add(iDKarariSucTipiInfo);

        });

        return result;
    }

    private List<SonlandirmaKarariHedefInfo> enrichHedefler(List<HedeflerTalep> list) {
        List<SonlandirmaKarariHedefInfo> result = new ArrayList<>();
        list.forEach(hedeflerTalep -> {

            //bu sonlandirmaya konu olan esas mahkeme karar bilgilerini bul(DetayMahkemeKararIslem de saklidir)
            DetayMahkemeKararTalep detayMahkemeKarar = null;
            Optional<HedeflerDetayTalep> hedeflerDetayOpt = dbHedeflerDetayService.findHedeflerDetayIslem(hedeflerTalep.getMahkemeKararTalepId(), hedeflerTalep.getHedefNo(), hedeflerTalep.getHedefTipi());
            if (hedeflerDetayOpt.isPresent()) {
                Long detayMahkemeKararIslemId = hedeflerDetayOpt.get().getDetayMahkemeKararTalepId();
                Optional<DetayMahkemeKararTalep> detayMahkemeKararOpt = dbDetayMahkemeKararService.findById(detayMahkemeKararIslemId);
                if (detayMahkemeKararOpt.isPresent()) {
                    detayMahkemeKarar = detayMahkemeKararOpt.get();
                }
            }
            DetayMahkemeKararInfo iliskiliMahkemeKararDetay = getMahkemeKararDetayInfo(detayMahkemeKarar);

            //Hedefin aidiyat listesi.
            List<HedeflerAidiyatTalep> hedeflerAidiyatList = dbHedeflerAidiyatService.findByHedeflerTalepId(hedeflerTalep.getId());
            List<IDHedefAidiyatInfo> aidiyatList = hedeflerIslemEnricherMapper.toIDHedefAidiyatInfoList(hedeflerAidiyatList);

            SonlandirmaKarariHedefInfo sonlandirmaKarariHedefInfo = hedeflerIslemEnricherMapper.toSonlandirmaKarariHedefInfo(hedeflerTalep);
            sonlandirmaKarariHedefInfo.setIliskiliMahkemeKararInfo(iliskiliMahkemeKararDetay);
            sonlandirmaKarariHedefInfo.setHedefAidiyatListesi(aidiyatList);

            result.add(sonlandirmaKarariHedefInfo);
        });

        return result;
    }

    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararTalep detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

    private List<IDKarariAidiyatInfo> enrichAidiyatListesi(List<MahkemeAidiyatTalep> aidiyatListesi) {
        return iDKararAidiyatIslemEnricherMapper.toIDKarariAidiyatList(aidiyatListesi);
    }

}



