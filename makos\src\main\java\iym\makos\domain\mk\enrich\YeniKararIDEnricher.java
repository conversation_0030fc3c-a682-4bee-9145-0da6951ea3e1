package iym.makos.domain.mk.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.enums.SureTip;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.HedeflerAidiyat;
import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import iym.common.model.entity.iym.mk.MahkemeSuclar;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mk.DbHedeflerAidiyatService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeAidiyatService;
import iym.common.service.db.mk.DbMahkemeSuclarService;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDYeniKararDetay;
import iym.makos.model.dto.view.info.*;
import iym.makos.model.dto.view.mapper.HedeflerIslemEnricherMapper;
import iym.makos.model.dto.view.mapper.KararAidiyatIslemEnricherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class YeniKararIDEnricher implements MKEnricher {

    private final DbHedeflerService dbHedeflerService;
    private final HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper;
    private final DbHedeflerAidiyatService dbHedeflerAidiyatService;
    private final DbMahkemeAidiyatService dbMahkemeAidiyatService;
    private final KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper;
    private final DbMahkemeSuclarService dbMahkemeSuclarService;
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public YeniKararIDEnricher(
            DbHedeflerService dbHedeflerService
            , HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper
            , DbHedeflerAidiyatService dbHedeflerAidiyatService
            , DbMahkemeAidiyatService dbMahkemeAidiyatService
            , KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper
            , DbMahkemeSuclarService dbMahkemeSuclarService
            , DbSucTipiService dbSucTipiService

    ) {
        this.dbHedeflerService = dbHedeflerService;
        this.hedeflerIslemEnricherMapper = hedeflerIslemEnricherMapper;
        this.dbHedeflerAidiyatService = dbHedeflerAidiyatService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;
        this.iDKararAidiyatIslemEnricherMapper = iDKararAidiyatIslemEnricherMapper;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKarar) {
        try {
            Long mahkemeKararId = mahkemeKarar.getMahkemeKararId();
            if (mahkemeKararId == null) {
                throw new Exception("YeniKararIDEnricher runs only if base.mahkemeKararId is not null");
            }

            //enrich only IDYeniKararDetay

            //Kararin Hedefler Listesi
            List<Hedefler> hedeflerList = dbHedeflerService.findByMahkemeKararId(mahkemeKararId);
            List<YeniIDKarariHedefInfo> iDYeniHedefInfoList = enrichHedefler(hedeflerList);

            //Kararin Aidiyat Listsi
            List<MahkemeAidiyat> aidiyatListesi = dbMahkemeAidiyatService.findByMahkemeKararId(mahkemeKararId);
            List<IDKarariAidiyatInfo> iDKarariAidiyatInfoList = enrichAidiyatListesi(aidiyatListesi);

            //Kararin Suc Tipleri
            List<MahkemeSuclar> sucLitesi = dbMahkemeSuclarService.findByMahkemeKararId(mahkemeKararId);
            List<IDKarariSucTipiInfo> sucTipleriDTOList = enrichSucTipleri(sucLitesi, mahkemeKararId);

            //IDMahkemeKarariInfo'in enrich edilecek nesnesi : MkIslemIDYeniKararDTO
            IDYeniKararDetay yeniKararDetay = IDYeniKararDetay.builder()
                    .hedefListesi(iDYeniHedefInfoList)
                    .hedefListesi(iDYeniHedefInfoList)
                    .mahkemeAidiyatlari(iDKarariAidiyatInfoList)
                    .sucTipleri(sucTipleriDTOList)
                    .build();
            mahkemeKarar.setIDYeniKararDetay(yeniKararDetay);
            return new Response<>(ResultCode.SUCCESS);

        } catch (Exception ex) {
            log.error("YeniKararIDEnricher failed. mahkemeKararId:{}", mahkemeKarar.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }

    private List<IDKarariSucTipiInfo> enrichSucTipleri(List<MahkemeSuclar> kararSucTipleri, Long mahkemeKararId) {
        List<IDKarariSucTipiInfo> result = new ArrayList<>();
        List<SucTipi> sucTipiListesi = dbSucTipiService.getByMahkemeIslemId(mahkemeKararId);
        CommonUtils.safeList(kararSucTipleri).forEach(mahkemeKararSucTipi -> {

            SucTipi sucTipi = sucTipiListesi.stream()
                    .filter(s -> s.getSucTipiKodu().equals(mahkemeKararSucTipi.getSucTipKodu()))
                    .findFirst()
                    .orElse(null);

            SucTipiInfo sucTipiInfo = SucTipiInfo.builder()
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipAdi(sucTipi != null ? sucTipi.getAciklama() : "")
                    .build();

            IDKarariSucTipiInfo iDKarariSucTipiInfo = IDKarariSucTipiInfo.builder()
                    .id(mahkemeKararSucTipi.getId())
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipi(sucTipiInfo)
                    .mahkemeKararId(mahkemeKararId)
                    .build();

            result.add(iDKarariSucTipiInfo);

        });

        return result;
    }

    private List<YeniIDKarariHedefInfo> enrichHedefler(List<Hedefler> list) {
        List<YeniIDKarariHedefInfo> result = new ArrayList<>();
        list.forEach(hedefler -> {

            IDHedefInfo iDHedefInfo = hedeflerIslemEnricherMapper.toIDHedefInfo(hedefler);

            YeniIDKarariHedefInfo yeniIDKarariHedefInfo = hedeflerIslemEnricherMapper.toYeniIDKarariHedefInfo(hedefler);

            //Hedeflerin aidiyatlarini set et.
            List<HedeflerAidiyat> hedeflerAidiyatList = dbHedeflerAidiyatService.findByHedeflerId(hedefler.getId());
            List<IDHedefAidiyatInfo> aidiyatList = hedeflerIslemEnricherMapper.toIDHedefAidiyatList(hedeflerAidiyatList);

            YeniIDKarariHedefInfo iDYeniHedefDetayInfo = YeniIDKarariHedefInfo.builder()
                    .hedefBilgileri(iDHedefInfo)
                    .baslamaTarihi(hedefler.getBaslamaTarihi())
                    .canakNo(hedefler.getCanakNo())
                    .sure(hedefler.getSuresi())
                    .sureTip(SureTip.fromValue(hedefler.getSureTipi()))
                    .hedefAidiyatListesi(aidiyatList)
                    .build();

            result.add(iDYeniHedefDetayInfo);
        });

        return result;
    }

    private List<IDKarariAidiyatInfo> enrichAidiyatListesi(List<MahkemeAidiyat> aidiyatListesi) {
        return aidiyatListesi.stream()
                .map(iDKararAidiyatIslemEnricherMapper::toIDKarariAidiyatInfo)
                .collect(Collectors.toList());
    }

}



