package iym.makos.domain.mk.enrich.service;

import iym.common.enums.KararTuru;
import iym.common.model.api.Response;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MkEnricherService {

    private final MkEnricherFactory mKEnricherFactory;

    @Autowired
    public MkEnricherService(MkEnricherFactory mKEnricherFactory) {
        this.mKEnricherFactory = mKEnricherFactory;
    }

    public Response<String> enrich(MahkemeKarariInfo iDMahkemeKarariInfo) {
        return mKEnricherFactory.enrich(iDMahkemeKarariInfo);
    }

    public Response<String> enrich(MahkemeKarariInfo iDMahkemeKarariInfo, KararTuru kararTuru) {
        return mKEnricherFactory.enrich(iDMahkemeKarariInfo, kararTuru);
    }

}
