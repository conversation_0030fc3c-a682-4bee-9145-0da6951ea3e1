package iym.makos.model.dto.view;

import iym.makos.model.dto.view.info.IDKarariAidiyatInfo;
import iym.makos.model.dto.view.info.IDKarariSucTipiInfo;
import iym.makos.model.dto.view.info.ITKarariHedefInfo;
import iym.makos.model.dto.view.info.YeniIDKarariHedefInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString
@EqualsAndHashCode
@Slf4j

public class ITYeniKararDetay {
    private List<ITKarariHedefInfo> hedefListesi;
}
