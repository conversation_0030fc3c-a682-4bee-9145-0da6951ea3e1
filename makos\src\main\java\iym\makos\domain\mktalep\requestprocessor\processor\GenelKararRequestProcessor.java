package iym.makos.domain.mktalep.requestprocessor.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.genelevrak.GenelEvrakRequest;
import iym.makos.model.dto.mktalep.request.it.GenelKararResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class GenelKararRequestProcessor extends MakosRequestProcessorBase<GenelEvrakRequest, GenelKararResponse> {

    @Override
    public GenelKararResponse process(GenelEvrakRequest request, MakosUserDetails islemYapanKullanici) {

        MakosApiResponse preCheck = preCheck(request, islemYapanKullanici);
        log.error("GenelEvrakRequest process failed: reason:{}", preCheck.getResponseMessage());
        if (preCheck.getResponseCode() != MakosResponseCode.SUCCESS) {
            return GenelKararResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage(preCheck.getResponseMessage())
                            .build())
                    .build();
        }

        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {
                return GenelKararResponse.builder()
                        .requestId(request.getId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            MkTalepDbSaveRequest<GenelEvrakRequest> saveRequest = MkTalepDbSaveRequest.<GenelEvrakRequest>builder()
                    .kararRequest(request)
                    .kullaniciId(islemYapanKullanici.getUserId())
                    .build();
            MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = requestSaver.kaydet(saveRequest);

            return GenelKararResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepIdWithEvrakId.getEvrakId())
                    .mahkemeKararTalepId(mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId())

                    .build();

        } catch (MakosResponseException ex) {
            // Null-safe logging - null checks already done above
            log.error("GenelEvrakRequest process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return GenelKararResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        } catch (Exception ex) {
            // Null-safe logging - null checks already done above
            log.error("GenelEvrakRequest process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return GenelKararResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        }
    }
}
