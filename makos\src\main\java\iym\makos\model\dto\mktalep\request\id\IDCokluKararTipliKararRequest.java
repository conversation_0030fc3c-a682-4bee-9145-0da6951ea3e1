package iym.makos.model.dto.mktalep.request.id;

import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDAidiyatBilgisiGuncellemeRequestDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDHedefGuncellemeRequestDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDSonlandirmaKarariRequestDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDUzatmaKarariRequestDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDYeniKararRequestDetay;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDCokluKararTipliKararRequest extends MkTalepRequest {

    @Valid
    private IDYeniKararRequestDetay yeniKararRequestDetay;

    @Valid
    private IDUzatmaKarariRequestDetay uzatmaKarariRequestDetay;

    @Valid
    private IDSonlandirmaKarariRequestDetay sonlandirmaKarariRequestDetay;

    @Valid
    private IDAidiyatBilgisiGuncellemeRequestDetay aidiyatBilgisiGuncellemeRequestDetay;

    @Valid
    private IDHedefGuncellemeRequestDetay hedefGuncellemeRequestDetay;


    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDCokluKararTipliKararRequest is valid");

        try {

            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_COKLU_KARAR_TIPLI_KARAR) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_COKLU_KARAR_TIPLI_KARAR.name() + " olmalıdır");
                return validationResult;
            }

            MahkemeKararTip mahkemeKararTipi = mahkemeKararBilgisi.getMahkemeKararTipi();
            boolean yeniMahkemeKararTipinde = CommonUtils.yeniMahkemeKararTipi(mahkemeKararTipi);
            if (!yeniMahkemeKararTipinde) {
                validationResult.addFailedReason("Mahkeme karar tipi, yeni karar için uygun değildir!");
            }

            int requestCount = 0;
            if (yeniKararRequestDetay != null) {
                requestCount++;
                ValidationResult yeniKararRequestDetayValid = yeniKararRequestDetay.isValid();
                if (!yeniKararRequestDetayValid.isValid()) {
                    yeniKararRequestDetayValid.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (uzatmaKarariRequestDetay != null) {
                requestCount++;
                ValidationResult uzatmaKarariRequestValid = uzatmaKarariRequestDetay.isValid();
                if (!uzatmaKarariRequestValid.isValid()) {
                    uzatmaKarariRequestValid.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (sonlandirmaKarariRequestDetay != null) {
                requestCount++;
                ValidationResult sonlandirmaKarariRequestValid = sonlandirmaKarariRequestDetay.isValid();
                if (!sonlandirmaKarariRequestValid.isValid()) {
                    sonlandirmaKarariRequestValid.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (aidiyatBilgisiGuncellemeRequestDetay != null) {
                requestCount++;
                ValidationResult aidiyatBilgisiGuncellemeRequestValid = aidiyatBilgisiGuncellemeRequestDetay.isValid();
                if (!aidiyatBilgisiGuncellemeRequestValid.isValid()) {
                    aidiyatBilgisiGuncellemeRequestValid.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (hedefGuncellemeRequestDetay != null) {
                requestCount++;
                ValidationResult hedefGuncellemeRequestValid = hedefGuncellemeRequestDetay.isValid();
                if (!hedefGuncellemeRequestValid.isValid()) {
                    hedefGuncellemeRequestValid.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (requestCount == 0) {
                validationResult.addFailedReason("En az 1 request detay dolu olmalıdır!");
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_COKLU_KARAR_TIPLI_KARAR;
    }
}

