package iym.common.util;

import iym.common.enums.ResponseCode;
import iym.common.model.api.FailedApiResponse;
import iym.common.model.api.ProblemDetailBuilder;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class ProblemDetailUtilsTest {

    @Test
    void testFromFailedApiResponse() {
        // Given
        UUID errorId = UUID.randomUUID();
        FailedApiResponse failedResponse = FailedApiResponse.builder()
                .errorId(errorId)
                .responseCode(ResponseCode.INVALID_REQUEST)
                .responseMessage("Test error message")
                .build();
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.fromFailedApiResponse(failedResponse);
        
        // Then
        assertNotNull(problemDetail);
        assertEquals(400, problemDetail.getStatus());
        assertEquals("Test error message", problemDetail.getDetail());
        assertEquals(errorId.toString(), problemDetail.getProperties().get("errorId"));
        assertEquals("INVALID_REQUEST", problemDetail.getProperties().get("responseCode"));
    }

    @Test
    void testFromFailedApiResponseWithNullValues() {
        // Given
        FailedApiResponse failedResponse = FailedApiResponse.builder()
                .responseCode(ResponseCode.FAILED)
                .build();
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.fromFailedApiResponse(failedResponse);
        
        // Then
        assertNotNull(problemDetail);
        assertEquals(500, problemDetail.getStatus());
        assertEquals("No error message provided", problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId")); // Auto-generated
        assertEquals("FAILED", problemDetail.getProperties().get("responseCode"));
    }

    @Test
    void testFromFailedApiResponseWithNull() {
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.fromFailedApiResponse(null);
        
        // Then
        assertNotNull(problemDetail);
        assertEquals(500, problemDetail.getStatus());
        assertEquals("Unknown error occurred", problemDetail.getDetail());
    }

    @Test
    void testForConstraintViolation() {
        // Given
        String validationMessage = "Field 'name' cannot be null";
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forConstraintViolation(validationMessage);
        
        // Then
        assertEquals(400, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.VALIDATION_ERROR, problemDetail.getType());
        assertEquals("Validation failed: " + validationMessage, problemDetail.getDetail());
    }

    @Test
    void testForHttpMessageNotReadable() {
        // Given
        String errorMessage = "JSON parse error";
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forHttpMessageNotReadable(errorMessage);
        
        // Then
        assertEquals(400, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.INVALID_REQUEST, problemDetail.getType());
        assertEquals("Invalid request format: " + errorMessage, problemDetail.getDetail());
    }

    @Test
    void testForMissingParameter() {
        // Given
        String parameterName = "userId";
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forMissingParameter(parameterName);
        
        // Then
        assertEquals(400, problemDetail.getStatus());
        assertEquals("Required parameter is missing: " + parameterName, problemDetail.getDetail());
    }

    @Test
    void testForIllegalArgumentInDevelopment() {
        // Given
        String errorMessage = "Invalid argument: negative value";
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forIllegalArgument(errorMessage, false);
        
        // Then
        assertEquals(400, problemDetail.getStatus());
        assertEquals(errorMessage, problemDetail.getDetail());
    }

    @Test
    void testForIllegalArgumentInProduction() {
        // Given
        String errorMessage = "Invalid argument: negative value";
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forIllegalArgument(errorMessage, true);
        
        // Then
        assertEquals(400, problemDetail.getStatus());
        assertEquals("Invalid argument provided", problemDetail.getDetail());
    }

    @Test
    void testForGenericExceptionInDevelopment() {
        // Given
        String errorMessage = "NullPointerException at line 123";
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forGenericException(errorMessage, false);
        
        // Then
        assertEquals(500, problemDetail.getStatus());
        assertEquals(errorMessage, problemDetail.getDetail());
    }

    @Test
    void testForGenericExceptionInProduction() {
        // Given
        String errorMessage = "NullPointerException at line 123";
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forGenericException(errorMessage, true);
        
        // Then
        assertEquals(500, problemDetail.getStatus());
        assertEquals("Internal Server Error", problemDetail.getDetail());
    }

    @Test
    void testForMakosApiException() {
        // Given
        String errorMessage = "MAKOS service unavailable";
        HttpStatus status = HttpStatus.SERVICE_UNAVAILABLE;
        
        // When
        ProblemDetail problemDetail = ProblemDetailUtils.forMakosApiException(errorMessage, status, false);
        
        // Then
        assertEquals(503, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.MAKOS_API_ERROR, problemDetail.getType());
        assertEquals(errorMessage, problemDetail.getDetail());
    }

    @Test
    void testGetErrorId() {
        // Given
        UUID errorId = UUID.randomUUID();
        ProblemDetail problemDetail = ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withErrorId(errorId)
                .build();
        
        // When
        String extractedErrorId = ProblemDetailUtils.getErrorId(problemDetail);
        
        // Then
        assertEquals(errorId.toString(), extractedErrorId);
    }

    @Test
    void testGetRequestId() {
        // Given
        UUID requestId = UUID.randomUUID();
        ProblemDetail problemDetail = ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withRequestId(requestId)
                .build();
        
        // When
        String extractedRequestId = ProblemDetailUtils.getRequestId(problemDetail);
        
        // Then
        assertEquals(requestId.toString(), extractedRequestId);
    }

    @Test
    void testGetResponseCode() {
        // Given
        ProblemDetail problemDetail = ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withResponseCode(ResponseCode.INVALID_REQUEST)
                .build();
        
        // When
        ResponseCode responseCode = ProblemDetailUtils.getResponseCode(problemDetail);
        
        // Then
        assertEquals(ResponseCode.INVALID_REQUEST, responseCode);
    }

    @Test
    void testGetResponseCodeFallback() {
        // Given - ProblemDetail without responseCode property
        ProblemDetail problemDetail = ProblemDetailBuilder.forStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();
        
        // When
        ResponseCode responseCode = ProblemDetailUtils.getResponseCode(problemDetail);
        
        // Then
        assertEquals(ResponseCode.FAILED, responseCode);
    }

    @Test
    void testIsValidationError() {
        // Given
        ProblemDetail validationError = ProblemDetailBuilder.validationError("Test validation error");
        ProblemDetail otherError = ProblemDetailBuilder.internalServerError("Test server error");
        
        // When & Then
        assertTrue(ProblemDetailUtils.isValidationError(validationError));
        assertFalse(ProblemDetailUtils.isValidationError(otherError));
    }

    @Test
    void testIsAuthenticationError() {
        // Given
        ProblemDetail authError = ProblemDetailBuilder.authenticationError("Test auth error");
        ProblemDetail otherError = ProblemDetailBuilder.internalServerError("Test server error");
        
        // When & Then
        assertTrue(ProblemDetailUtils.isAuthenticationError(authError));
        assertFalse(ProblemDetailUtils.isAuthenticationError(otherError));
    }

    @Test
    void testIsAuthorizationError() {
        // Given
        ProblemDetail authzError = ProblemDetailBuilder.authorizationError("Test authz error");
        ProblemDetail otherError = ProblemDetailBuilder.internalServerError("Test server error");
        
        // When & Then
        assertTrue(ProblemDetailUtils.isAuthorizationError(authzError));
        assertFalse(ProblemDetailUtils.isAuthorizationError(otherError));
    }

    @Test
    void testIsMakosApiError() {
        // Given
        ProblemDetail makosError = ProblemDetailBuilder.makosApiError("Test MAKOS error", HttpStatus.BAD_GATEWAY);
        ProblemDetail otherError = ProblemDetailBuilder.internalServerError("Test server error");
        
        // When & Then
        assertTrue(ProblemDetailUtils.isMakosApiError(makosError));
        assertFalse(ProblemDetailUtils.isMakosApiError(otherError));
    }

    @Test
    void testWithEnvironmentDetailsInDevelopment() {
        // Given
        ProblemDetail problemDetail = ProblemDetailBuilder.internalServerError("Test error");
        Exception exception = new RuntimeException("Test exception");
        
        // When
        ProblemDetail enhanced = ProblemDetailUtils.withEnvironmentDetails(problemDetail, true, exception);
        
        // Then
        assertEquals("RuntimeException", enhanced.getProperties().get("exceptionClass"));
        assertNotNull(enhanced.getProperties().get("stackTrace"));
    }

    @Test
    void testWithEnvironmentDetailsInProduction() {
        // Given
        ProblemDetail problemDetail = ProblemDetailBuilder.internalServerError("Test error");
        Exception exception = new RuntimeException("Test exception");
        
        // When
        ProblemDetail enhanced = ProblemDetailUtils.withEnvironmentDetails(problemDetail, false, exception);
        
        // Then
        assertNull(enhanced.getProperties().get("exceptionClass"));
        assertNull(enhanced.getProperties().get("stackTrace"));
    }
}
