package iym.makos.model.dto.view;

import iym.common.enums.KararTuru;
import iym.makos.model.dto.view.info.EvrakDetayInfo;
import iym.makos.model.dto.view.info.MahkemeKararBilgisiInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Data
@NoArgsConstructor
@SuperBuilder
@ToString
@Slf4j
public class MahkemeKarariInfo {

    private UUID requestId;

    private Long mahkemeKararId;

    private KararTuru kararTuru;

    private EvrakDetayInfo evrakDetay;

    protected MahkemeKararBilgisiInfo mahkemeKararBilgisi;

    private String dosyaAdi;

    private String durumu;


    // optional fields
    private IDYeniKararDetay iDYeniKararDetay;

    private IDUzatmaKararDetay iDUzatmaKararDetay;

    private IDSonlandirmaKararDetay iDSonlandirmaKararDetay;

    private IDAidiyatGuncellemeKararDetay iDAidiyatGuncellemeKararDetay;

    private IDHedefGuncellemeKararDetay iDHedefGuncellemeKararDetay;

    private IDMahkemeBilgiGuncellemeKararDetay iDMahkemeBilgiGuncellemeKararDetay;

    private IDSucTipiGuncellemeKararDetay iDSucTipiGuncellemeKararDetay;

    private ITYeniKararDetay iTYeniKararDetay;

}
