package iym.makos.controller;

import iym.common.model.api.Response;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.mktalep.stateupdater.service.MkTalepStateUpdaterService;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.evrak.EvrakDurumGuncelleRequest;
import iym.makos.model.dto.evrak.EvrakDurumGuncelleResponse;
import iym.makos.model.dto.evrak.IslenecekEvrakRequest;
import iym.makos.model.dto.evrak.IslenecekEvrakResponse;
import iym.makos.service.EvrakKayitService;
import iym.makos.service.mk.MahkemeKararService;
import iym.makos.service.mktalep.HtsMahkemeKararTalepService;
import iym.makos.service.mktalep.MahkemeKararTalepService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/evrak")
@Slf4j
@Validated
public class EvrakController {

    @Autowired
    private MahkemeKararService mahkemeKararService;

    @Autowired
    private MkTalepStateUpdaterService mkTalepStateUpdaterService;

    @Autowired
    private EvrakKayitService evrakKayitService;

    @Autowired
    private MahkemeKararTalepService mahkemeKararTalepService;

    @Autowired
    private HtsMahkemeKararTalepService htsMahkemeKararTalepService;

    @PostMapping("/islenecekEvrakListesi")
    public ResponseEntity<IslenecekEvrakResponse> islenecekKararListele(
            @NotNull @Valid @RequestBody IslenecekEvrakRequest request, Authentication authentication) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            String kurumKodu = user.getKullaniciKurum().getValue();
            kurumKodu = "02";
            Response<IslenecekEvrakResponse> islenecekKararListesiResponse = evrakKayitService.islenecekEvrakListesi(kurumKodu, PageRequest.of(request.getPage(), request.getSize()));

            if (!islenecekKararListesiResponse.isSuccess()) {
                return ResponseEntity.ok(IslenecekEvrakResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.FAILED)
                                .responseMessage("İşlenecek Evrak Listesi Başarısız : " + islenecekKararListesiResponse.getResultDetails())
                                .build())
                        .build());
            }

            return ResponseEntity.ok(IslenecekEvrakResponse.builder()
                    .islenecekEvraklar(islenecekKararListesiResponse.getResult().getIslenecekEvraklar())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("islenecekKararlariListele process failed, requestId:{}", request.hashCode(), ex);
            return ResponseEntity.ok(IslenecekEvrakResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("İşlenecek Evrak Listesi Başarısız")
                            .build())
                    .build());
        }
    }



    @PostMapping("/evrakDurumGuncelle")
    public ResponseEntity<EvrakDurumGuncelleResponse> evrakDurumGuncelle(
            @NotNull @Valid @RequestBody EvrakDurumGuncelleRequest request, Authentication authentication) {

        log.debug("Evrak durum güncelleme request received. evrakId:{}, request:{}", request.getEvrakId(), request.getId());

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();
            Response<EvrakDurumGuncelleResponse> response = evrakKayitService.evrakDurumGüncelle(request.getEvrakId(), request.getTalepGuncellemeTuru(), user);

            if(response.isSuccess()){
                return ResponseEntity.ok(EvrakDurumGuncelleResponse.builder()
                        .evrakId(request.getEvrakId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.SUCCESS)
                                .responseMessage("Evrak Durum Güncelleme Başarılı : " + response.getResultDetails())
                                .build())
                        .build());

            }
            else{
                return ResponseEntity.ok(EvrakDurumGuncelleResponse.builder()
                        .evrakId(request.getEvrakId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.FAILED)
                                .responseMessage("Evrak Durum Güncelleme Başarısız : " + response.getResultDetails())
                                .build())
                        .build());
            }

        } catch (Exception ex) {
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, evrakId:{}", request.getId(), request.getEvrakId(), ex);
            return ResponseEntity.ok(EvrakDurumGuncelleResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Evrak Durum Güncelleme Başarısız : " + ex.getMessage())
                            .build())
                    .build());
        }
    }




}
