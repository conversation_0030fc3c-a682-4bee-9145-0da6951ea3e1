package iym.makos.model.dto.mktalep.request.id.detay;

import iym.common.validation.ValidationResult;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class IDMahkemeKararAtamaRequestDetay implements IDRequestDetay {

    @NotNull
    Long evrakId;

    @NotNull
    Long atananKullaniciId;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDMahkemeKararAtamaRequestDetay is valid");

        try {

            ValidationResult validationResult = new ValidationResult(true);

            if (evrakId == null) {
                validationResult.addFailedReason("EvrakId boş olamaz.");
            }

            if (atananKullaniciId == null) {
                validationResult.addFailedReason("AtananKullanici Id boş olamaz.");
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

}

