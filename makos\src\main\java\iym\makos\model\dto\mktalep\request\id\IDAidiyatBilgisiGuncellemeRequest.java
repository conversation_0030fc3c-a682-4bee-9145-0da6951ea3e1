package iym.makos.model.dto.mktalep.request.id;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDAidiyatBilgisiGuncellemeRequestDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDAidiyatBilgisiGuncellemeRequest extends MkTalepRequest {

    @NotNull
    @Valid
    @Schema(description = "Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri")
    private IDAidiyatBilgisiGuncellemeRequestDetay aidiyatBilgisiGuncellemeRequestDetay;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if AidiyatBilgisiGuncellemeRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME.name() + " olmalıdır");
                return validationResult;
            }

            if (aidiyatBilgisiGuncellemeRequestDetay == null) {
                validationResult.addFailedReason("AidiyatBilgisiGuncellemeRequestDetay boş olamaz");
                return validationResult;
            }

            MahkemeKararTip kararTip = mahkemeKararBilgisi.getMahkemeKararTipi();
            if (kararTip != MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME) {
                validationResult.addFailedReason("Mahkeme karar Tipi " + MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME.name() + " olmalıdır");
            }

            ValidationResult requestDetayValid = aidiyatBilgisiGuncellemeRequestDetay.isValid();
            if (!requestDetayValid.isValid()){
                requestDetayValid.getReasons().forEach(validationResult::addFailedReason);
            }

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }
}

