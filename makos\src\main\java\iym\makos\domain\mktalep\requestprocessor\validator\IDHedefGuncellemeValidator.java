package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.enums.HedefTip;
import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mktalep.request.id.IDHedefGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.api.*;
import iym.makos.model.dto.mktalep.request.id.detay.IDHedefGuncellemeRequestDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDSonlandirmaKarariRequestDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class IDHedefGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDHedefGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbHedeflerService dbHedeflerService;

    @Autowired
    public IDHedefGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService,
                                      DbHedeflerService dbHedeflerService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbHedeflerService = dbHedeflerService;
    }

    @Override
    protected ValidationResult doValidate(IDHedefGuncellemeRequest request) {

        try {
            ValidationResult validationResult = new ValidationResult(true);

            IDHedefGuncellemeRequestDetay kararRequestDetay = request.getHedefGuncellemeRequestDetay();

            if (kararRequestDetay == null) {
                validationResult.addFailedReason("IDHedefGuncellemeRequestDetay boş olamaz");
                return validationResult;
            }

            for (HedefGuncellemeKararDetay adSoyadGuncellemeDetay : kararRequestDetay.getHedefGuncellemeKararDetayListesi()) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = adSoyadGuncellemeDetay.getMahkemeKararDetay();
                Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetay.getMahkemeKodu()
                        , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetay.getSorusturmaNo());

                if (iliskiliMahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu(), iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());
                    validationResult.addFailedReason(errorStr);
                } else {
                    MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();

                    for (HedefGuncellemeDetay hedefGuncellemeDetay : CommonUtils.safeList(adSoyadGuncellemeDetay.getHedefGuncellemeDetayListesi())) {

                        String hedefNo = hedefGuncellemeDetay.getHedef().getHedefNo();
                        HedefTip hedefTip = hedefGuncellemeDetay.getHedef().getHedefTip();
                        String yeniHedefAd = "";
                        String yeniHedefSoyad = "";
                        String yeniCanakNo = "";
                        String yeniTcKimlikNo = "";

                        HedeflerDetayTalep hedeflerDetayTalep = new HedeflerDetayTalep();
                        for(HedefGuncellemeBilgi hedefGuncellemeBilgisi : hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi()){
                            if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.AD){
                                yeniHedefAd = hedefGuncellemeBilgisi.getYeniDegeri();
                            }
                            else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.SOYAD){
                                yeniHedefSoyad = hedefGuncellemeBilgisi.getYeniDegeri();
                            }
                            else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.TCKIMlIKNO){
                                yeniTcKimlikNo = hedefGuncellemeBilgisi.getYeniDegeri();
                            }
                            else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.CANAK_NO){
                                yeniCanakNo = hedefGuncellemeBilgisi.getYeniDegeri();
                            }
                        }

                        //Hedef guncellemede en az bir alan dolu olmalıdır.
                        if(CommonUtils.isNullOrEmpty(yeniHedefAd) && CommonUtils.isNullOrEmpty(yeniHedefSoyad) && CommonUtils.isNullOrEmpty(yeniCanakNo) && CommonUtils.isNullOrEmpty(yeniTcKimlikNo)){
                            validationResult.addFailedReason(hedefNo + " için en az bir güncelleme  alanı olmalıdır. ");
                        }
                        else {
                            //Sistemde bu hedef var mi?
                            Optional<Hedefler> existingHedefBilgisiOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, hedefTip.getHedefKodu());

                            if (existingHedefBilgisiOpt.isEmpty()) {
                                validationResult.addFailedReason(hedefNo + " numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
                            } else {
                                Hedefler existingHedefBilgisi = existingHedefBilgisiOpt.get();

                                for(HedefGuncellemeBilgi hedefGuncellemeBilgisi : hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi()){

                                    if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.AD){
                                        yeniHedefAd = hedefGuncellemeBilgisi.getYeniDegeri();
                                        if(existingHedefBilgisi.getHedefAdi().equals(yeniHedefAd) ){
                                            validationResult.addFailedReason(hedefNo + " numaralı hedefin ad bilgisi eskisi ile aynı.");
                                        }
                                    }
                                    else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.SOYAD){
                                        yeniHedefSoyad = hedefGuncellemeBilgisi.getYeniDegeri();
                                        if(existingHedefBilgisi.getHedefSoyadi().equals(yeniHedefSoyad) ){
                                            validationResult.addFailedReason(hedefNo + " numaralı hedefin soyad bilgisi eskisi ile aynı.");
                                        }
                                    }
                                    else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.TCKIMlIKNO){
                                        yeniTcKimlikNo = hedefGuncellemeBilgisi.getYeniDegeri();
                                        if(existingHedefBilgisi.getTcKimlikNo().equals(yeniTcKimlikNo) ){
                                            validationResult.addFailedReason(hedefNo + " numaralı hedefin TC Kimlik numarası eskisi ile aynı.");
                                        }
                                    }
                                    else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.CANAK_NO){
                                        yeniCanakNo = hedefGuncellemeBilgisi.getYeniDegeri();
                                         if(existingHedefBilgisi.getCanakNo().equals(yeniCanakNo) ){
                                            validationResult.addFailedReason(hedefNo + " numaralı hedefin CANAK numarası eskisi ile aynı.");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME;
    }

}

