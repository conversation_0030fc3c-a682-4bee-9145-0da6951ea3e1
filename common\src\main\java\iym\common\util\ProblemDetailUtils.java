package iym.common.util;

import iym.common.enums.ResponseCode;
import iym.common.model.api.FailedApiResponse;
import iym.common.model.api.ProblemDetailBuilder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;

import java.util.UUID;

/**
 * Utility class for working with ProblemDetail instances and converting
 * from legacy error response formats.
 */
public class ProblemDetailUtils {

    /**
     * Convert a FailedApiResponse to ProblemDetail format
     */
    public static ProblemDetail fromFailedApiResponse(FailedApiResponse failedResponse) {
        if (failedResponse == null) {
            return ProblemDetailBuilder.internalServerError("Unknown error occurred");
        }

        ResponseCode responseCode = failedResponse.getResponseCode();
        String detail = failedResponse.getResponseMessage();
        UUID errorId = failedResponse.getErrorId();

        ProblemDetailBuilder builder = ProblemDetailBuilder.fromResponseCode(responseCode)
                .withDetail(detail != null ? detail : "No error message provided")
                .withResponseCode(responseCode);

        if (errorId != null) {
            builder.withErrorId(errorId);
        } else {
            builder.withErrorId();
        }

        return builder.build();
    }

    /**
     * Create a ProblemDetail for constraint validation errors
     */
    public static ProblemDetail forConstraintViolation(String validationMessage) {
        return ProblemDetailBuilder.validationError("Validation failed: " + validationMessage);
    }

    /**
     * Create a ProblemDetail for HTTP message not readable errors
     */
    public static ProblemDetail forHttpMessageNotReadable(String errorMessage) {
        return ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withType(ProblemDetailBuilder.ProblemTypes.INVALID_REQUEST)
                .withTitle(ProblemDetailBuilder.ProblemTitles.INVALID_REQUEST)
                .withDetail("Invalid request format: " + errorMessage)
                .withErrorId()
                .withResponseCode(ResponseCode.FAILED)
                .build();
    }

    /**
     * Create a ProblemDetail for missing request parameters
     */
    public static ProblemDetail forMissingParameter(String parameterName) {
        return ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withType(ProblemDetailBuilder.ProblemTypes.INVALID_REQUEST)
                .withTitle(ProblemDetailBuilder.ProblemTitles.INVALID_REQUEST)
                .withDetail("Required parameter is missing: " + parameterName)
                .withErrorId()
                .withResponseCode(ResponseCode.FAILED)
                .build();
    }

    /**
     * Create a ProblemDetail for illegal argument exceptions
     */
    public static ProblemDetail forIllegalArgument(String errorMessage, boolean isProduction) {
        String safeMessage = isProduction ? "Invalid argument provided" : errorMessage;
        return ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withType(ProblemDetailBuilder.ProblemTypes.INVALID_REQUEST)
                .withTitle(ProblemDetailBuilder.ProblemTitles.INVALID_REQUEST)
                .withDetail(safeMessage)
                .withErrorId()
                .withResponseCode(ResponseCode.INVALID_REQUEST)
                .build();
    }

    /**
     * Create a ProblemDetail for conversion failed exceptions
     */
    public static ProblemDetail forConversionFailed(String errorMessage, boolean isProduction) {
        String safeMessage = isProduction ? "Invalid parameter format" : errorMessage;
        return ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withType(ProblemDetailBuilder.ProblemTypes.INVALID_REQUEST)
                .withTitle(ProblemDetailBuilder.ProblemTitles.INVALID_REQUEST)
                .withDetail(safeMessage)
                .withErrorId()
                .withResponseCode(ResponseCode.FAILED)
                .build();
    }

    /**
     * Create a ProblemDetail for generic exceptions
     */
    public static ProblemDetail forGenericException(String errorMessage, boolean isProduction) {
        String safeMessage = isProduction ? "Internal Server Error" : errorMessage;
        return ProblemDetailBuilder.internalServerError(safeMessage);
    }

    /**
     * Create a ProblemDetail for MAKOS API client exceptions
     */
    public static ProblemDetail forMakosApiException(String errorMessage, HttpStatus httpStatus, boolean isProduction) {
        String safeMessage = errorMessage;
        if (httpStatus == HttpStatus.INTERNAL_SERVER_ERROR && isProduction) {
            safeMessage = "Internal Server Error";
        }
        return ProblemDetailBuilder.makosApiError(safeMessage, httpStatus);
    }

    /**
     * Extract error ID from ProblemDetail if present
     */
    public static String getErrorId(ProblemDetail problemDetail) {
        if (problemDetail.getProperties() == null) {
            return null;
        }
        Object errorId = problemDetail.getProperties().get("errorId");
        return errorId != null ? errorId.toString() : null;
    }

    /**
     * Extract request ID from ProblemDetail if present
     */
    public static String getRequestId(ProblemDetail problemDetail) {
        if (problemDetail.getProperties() == null) {
            return null;
        }
        Object requestId = problemDetail.getProperties().get("requestId");
        return requestId != null ? requestId.toString() : null;
    }

    /**
     * Extract ResponseCode from ProblemDetail if present (for backward compatibility)
     */
    public static ResponseCode getResponseCode(ProblemDetail problemDetail) {
        if (problemDetail.getProperties() != null) {
            Object responseCode = problemDetail.getProperties().get("responseCode");
            if (responseCode != null) {
                try {
                    return ResponseCode.valueOf(responseCode.toString());
                } catch (IllegalArgumentException e) {
                    // Fallback based on HTTP status
                    return mapHttpStatusToResponseCode(HttpStatus.valueOf(problemDetail.getStatus()));
                }
            }
        }
        return mapHttpStatusToResponseCode(HttpStatus.valueOf(problemDetail.getStatus()));
    }

    /**
     * Map HTTP status to ResponseCode for backward compatibility
     */
    private static ResponseCode mapHttpStatusToResponseCode(HttpStatus status) {
        if (status.is2xxSuccessful()) {
            return ResponseCode.SUCCESS;
        } else if (status.is4xxClientError()) {
            return ResponseCode.INVALID_REQUEST;
        } else {
            return ResponseCode.FAILED;
        }
    }

    /**
     * Check if a ProblemDetail represents a validation error
     */
    public static boolean isValidationError(ProblemDetail problemDetail) {
        return ProblemDetailBuilder.ProblemTypes.VALIDATION_ERROR.equals(problemDetail.getType());
    }

    /**
     * Check if a ProblemDetail represents an authentication error
     */
    public static boolean isAuthenticationError(ProblemDetail problemDetail) {
        return ProblemDetailBuilder.ProblemTypes.AUTHENTICATION_ERROR.equals(problemDetail.getType());
    }

    /**
     * Check if a ProblemDetail represents an authorization error
     */
    public static boolean isAuthorizationError(ProblemDetail problemDetail) {
        return ProblemDetailBuilder.ProblemTypes.AUTHORIZATION_ERROR.equals(problemDetail.getType());
    }

    /**
     * Check if a ProblemDetail represents a MAKOS API error
     */
    public static boolean isMakosApiError(ProblemDetail problemDetail) {
        return ProblemDetailBuilder.ProblemTypes.MAKOS_API_ERROR.equals(problemDetail.getType());
    }

    /**
     * Add environment-specific error details
     */
    public static ProblemDetail withEnvironmentDetails(ProblemDetail problemDetail, boolean isDevelopment, Throwable exception) {
        if (isDevelopment && exception != null) {
            problemDetail.setProperty("exceptionClass", exception.getClass().getSimpleName());
            problemDetail.setProperty("stackTrace", getStackTraceAsString(exception));
        }
        return problemDetail;
    }

    /**
     * Convert stack trace to string for development environments
     */
    private static String getStackTraceAsString(Throwable exception) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        exception.printStackTrace(pw);
        return sw.toString();
    }
}
