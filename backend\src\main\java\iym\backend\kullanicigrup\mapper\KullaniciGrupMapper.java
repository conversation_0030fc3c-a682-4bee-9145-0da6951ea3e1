package iym.backend.kullanicigrup.mapper;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingInheritanceStrategy;
import org.mapstruct.MappingTarget;
import iym.backend.kullanicigrup.dto.KullaniciGrupDto;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.shared.mapper.BaseMapper;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring",
        mappingInheritanceStrategy = MappingInheritanceStrategy.AUTO_INHERIT_ALL_FROM_CONFIG)
public interface KullaniciGrupMapper extends BaseMapper<KullaniciGrup, KullaniciGrupDto> {

    @AfterMapping
    default void fillYetkiIdList(KullaniciGrup entity, @MappingTarget KullaniciGrupDto dto) {
        if (entity.getKullaniciGrupYetkiler() != null) {
            List<Long> yetkiIdList = entity.getKullaniciGrupYetkiler().stream()
                    .map(kg -> kg.getYetki().getId())
                    .collect(Collectors.toList());
            dto.setYetkiIdList(yetkiIdList);
        }
    }
}

