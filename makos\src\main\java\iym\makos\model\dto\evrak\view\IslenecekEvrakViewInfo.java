package iym.makos.model.dto.evrak.view;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.EvrakTuru;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description =  "İşlenecek Evrak Bilgisi")
public class IslenecekEvrakViewInfo {


    @Schema(description = "Evrak Id")
    private Long evrakId;

    @Schema(description = "Evrak Türü")
    private EvrakTuru evrakTuru;

    @Schema(description = "Evrak Sıra No")
    private String evrakSiraNo;

    @Schema(description = "Kurum Evrak No")
    private String kurumEvrakNo;

    @Schema(description = "Kurum Evrak Tarihi")
    private LocalDateTime kurumEvrakTarihi;

    @Schema(description = "<PERSON>ıt Tarihi")
    private LocalDateTime kayitTarihi;

    @Schema(description = "Mahkeme İl/İlçe Kodu")
    private String geldigiIlIlceKodu;

    @Schema(description = "Mahkeme İl/İlçe Adı")
    private String geldigiIlIlceAdi;

    @Schema(description = "Kaydeden Kullanıcı Id")
    private Long kaydedenKullaniciId;

    @Schema(description = "Kaydeden KullanıcıAdi")
    private String kaydedenKullaniciAdi;

    @Schema(description = "Kaydeden Adı/Soyadı")
    private String kaydedenAdiSoyadi;

    @Schema(description = "Soruşturma No")
    private String sorusturmaNo;

    @Schema(description = "Mahkeme Karar No")
    private String mahkemeKararNo;

    @Schema(description = "Açıklama")
    private String aciklama;

    @Schema(description = "Durumu")
    private String durumu;

}

