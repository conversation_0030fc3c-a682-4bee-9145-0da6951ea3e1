package iym.makos.domain.mkislem.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.mkislem.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.mkislem.MahkemeAidiyatDetayIslem;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mkislem.DbDetayMahkemeKararIslemService;
import iym.common.service.db.mkislem.DbMahkemeAidiyatDetayIslemService;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.IDAidiyatGuncellemeKararDetay;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.MahkemeAidiyatDetayDTO;
import iym.makos.model.dto.view.info.MahkemeKararAidiyatGuncellemeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class AidiyatGuncellemeIslemEnricher implements MKIslemEnricher {

    private final DbDetayMahkemeKararIslemService dbDetayMahkemeKararService;
    private final DbMahkemeAidiyatDetayIslemService dbMahkemeAidiyatDetayService;

    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;


    @Autowired
    public AidiyatGuncellemeIslemEnricher(DbDetayMahkemeKararIslemService dbDetayMahkemeKararService
            , DbMahkemeAidiyatDetayIslemService dbMahkemeAidiyatDetayService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbMahkemeAidiyatDetayService = dbMahkemeAidiyatDetayService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }


    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {

        try {

            Long mahkemeKararIslemId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararIslemId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararIslemId yok");
            }

            List<MahkemeKararAidiyatGuncellemeInfo> aidiyatGuncellemeListesi = new ArrayList<>();

            List<DetayMahkemeKararIslem> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKararTalep -> {

                DetayMahkemeKararInfo detayMahkemeKararInfo = getMahkemeKararDetayInfo(detayMahkemeKararTalep);

                List<MahkemeAidiyatDetayIslem> list = dbMahkemeAidiyatDetayService.findByMahkemeKararDetayIslemId(detayMahkemeKararTalep.getId());
                List<MahkemeAidiyatDetayDTO> dtoList = getMahkemeAidiyatDetayDTOList(list);

                MahkemeKararAidiyatGuncellemeInfo mahkemeKararAidiyatGuncellemeInfo = MahkemeKararAidiyatGuncellemeInfo.builder()
                        .detayMahkemeKararInfo(detayMahkemeKararInfo)
                        .aidiyatGuncellemeListesi(dtoList)
                        .build();

                aidiyatGuncellemeListesi.add(mahkemeKararAidiyatGuncellemeInfo);

            });

            IDAidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay = IDAidiyatGuncellemeKararDetay.builder()
                    .aidiyatGuncellemeListesi(aidiyatGuncellemeListesi)
                    .build();

            mahkemeKararIslem.setIDAidiyatGuncellemeKararDetay(aidiyatGuncellemeKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("AidiyatGuncellemeKararEnricher failed. mahkemeKararIslemId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }


    public List<MahkemeAidiyatDetayDTO> getMahkemeAidiyatDetayDTOList(List<MahkemeAidiyatDetayIslem> list) {
        List<MahkemeAidiyatDetayDTO> result = new ArrayList<>();

        CommonUtils.safeList(list).forEach(mahkemeAidiyatDetayIslem -> {
            MahkemeAidiyatDetayDTO dd = MahkemeAidiyatDetayDTO.builder()
                    .detayMahkemeKararId(mahkemeAidiyatDetayIslem.getDetayMahkemeKararIslemId())
                    .id(mahkemeAidiyatDetayIslem.getId())
                    .iliskiliMahkemeKararId(mahkemeAidiyatDetayIslem.getIliskiliMahkemeKararId())
                    .tarih(mahkemeAidiyatDetayIslem.getTarih())
                    .durum(mahkemeAidiyatDetayIslem.getDurum())
                    .mahkemeAidiyatKoduCikar(mahkemeAidiyatDetayIslem.getMahkemeAidiyatKoduCikar())
                    .mahkemeAidiyatKoduEkle(mahkemeAidiyatDetayIslem.getMahkemeAidiyatKoduEkle())
                    .mahkemeKararId(mahkemeAidiyatDetayIslem.getMahkemeKararIslemId())
                    .build();
            result.add(dd);
        });

        return result;
    }


    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararIslem detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

}



