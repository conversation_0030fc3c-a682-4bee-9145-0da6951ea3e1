package iym.makos.model.dto.mktalep.request.genelevrak;

import iym.common.enums.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class GenelEvrakRequest extends MkTalepRequest {

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if GenelEvrakRequest is valid");
        ValidationResult validationResult = new ValidationResult(true);
        try {

            if (kararTuru != KararTuru.GENEL_EVRAK) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.GENEL_EVRAK.name() + " olmalıdır");
                return validationResult;
            }

            // TODO
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
        return validationResult;
    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.GENEL_EVRAK;
    }
}

