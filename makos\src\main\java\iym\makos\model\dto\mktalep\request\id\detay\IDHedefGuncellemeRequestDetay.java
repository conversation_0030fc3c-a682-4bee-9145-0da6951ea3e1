package iym.makos.model.dto.mktalep.request.id.detay;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.HedefGuncellemeDetay;
import iym.makos.model.api.HedefGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class IDHedefGuncellemeRequestDetay implements IDRequestDetay {

    @NotNull
    @Valid
    @Size(min = 1)
    @Schema(description = "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara ait güncellenecek ad, soyad bilgileri")
    private List<HedefGuncellemeKararDetay> hedefGuncellemeKararDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if HedefGuncellemeRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (hedefGuncellemeKararDetayListesi == null || hedefGuncellemeKararDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Güncellemeye konu olan en az bir detay girilmelidir!");
                return validationResult;
            }

            for (HedefGuncellemeKararDetay hedefGuncellemeKararDetay : hedefGuncellemeKararDetayListesi) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = hedefGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                }
                List<HedefGuncellemeDetay> hedefListesi = hedefGuncellemeKararDetay.getHedefGuncellemeDetayListesi();
                if (hedefListesi == null || hedefListesi.isEmpty()) {
                    validationResult.addFailedReason("Güncelleme yapılacak hedef listesi boş olamaz!");
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

}

