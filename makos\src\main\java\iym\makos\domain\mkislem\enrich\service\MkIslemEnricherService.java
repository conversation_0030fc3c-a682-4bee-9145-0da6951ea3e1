package iym.makos.domain.mkislem.enrich.service;

import iym.common.enums.KararTuru;
import iym.common.model.api.Response;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MkIslemEnricherService {

    private final MkIslemEnricherFactory mkIslemEnricherFactory;

    @Autowired
    public MkIslemEnricherService(MkIslemEnricherFactory mkIslemEnricherFactory) {
        this.mkIslemEnricherFactory = mkIslemEnricherFactory;
    }

    public Response<String> enrich(MahkemeKarariInfo iDMahkemeKarariInfo) {
        return mkIslemEnricherFactory.enrich(iDMahkemeKarariInfo);
    }

    public Response<String> enrich(MahkemeKarariInfo iDMahkemeKarariInfo, KararTuru kararTuru) {
        return mkIslemEnricherFactory.enrich(iDMahkemeKarariInfo, kararTuru);
    }

}
