openapi: 3.1.0
info:
  title: MAKOS OpenAPI definition
  description: MAKOS Application
  version: v1.0
servers:
- url: http://localhost:5000/makosapi
  description: Generated server url
security:
- BasicAuth: []
paths:
  /user/update:
    post:
      tags:
      - user-controller
      operationId: updateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateUserRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/UpdateUserResponse"
  /user/delete:
    post:
      tags:
      - user-controller
      operationId: deleteUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeleteUserRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/DeleteUserResponse"
  /user/deactivate:
    post:
      tags:
      - user-controller
      operationId: deactivateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UsernameRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/DeactivateUserResponse"
  /user/add:
    post:
      tags:
      - user-controller
      operationId: addUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddUserRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/AddUserResponse"
  /user/activate:
    post:
      tags:
      - user-controller
      operationId: activateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UsernameRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ActivateUserResponse"
  /mahkemeKararTalep/yeniKararID:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: yeniKararID
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  $ref: "#/components/schemas/IDYeniKararRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IDYeniKararResponse"
  /mahkemeKararTalep/uzatmaKarariID:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: uzatmaKarariID
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  $ref: "#/components/schemas/IDUzatmaKarariRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDUzatmaKarariResponse"
  /mahkemeKararTalep/sucTipiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: sucTipiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  $ref: "#/components/schemas/IDSucTipiGuncellemeRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDSucTipiGuncellemeResponse"
  /mahkemeKararTalep/sonlandirmaKarariID:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: sonlandirmaKarariID
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  $ref: "#/components/schemas/IDSonlandirmaKarariRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDSonlandirmaKarariResponse"
  /mahkemeKararTalep/mahkemeKararTalepSorgu:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: mahkemeKararTalepSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararTalepSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararTalepSorgulamaResponse"
  /mahkemeKararTalep/mahkemeKararTalepBilgisi:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: mahkemeKararTalepBilgisi
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MahkemeKararTalepBilgisiRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/MahkemeKararTalepQueryResponse"
  /mahkemeKararTalep/mahkemeBilgisiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: mahkemeBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  $ref: "#/components/schemas/IDMahkemeKararGuncellemeRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararGuncellemeResponse"
  /mahkemeKararTalep/hedefBilgisiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: hedefBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  $ref: "#/components/schemas/IDHedefGuncellemeRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDHedefGuncellemeResponse"
  /mahkemeKararTalep/aidiyatBilgisiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: aidiyatBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  $ref: "#/components/schemas/IDAidiyatBilgisiGuncellemeRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDAidiyatBilgisiGuncellemeResponse"
  /mahkemeKararIslem/mahkemeKararTalepIslemSorguID:
    post:
      tags:
      - mahkeme-karar-islem-controller
      operationId: mahkemeKararTalepIslemSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararTalepSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararTalepSorgulamaResponse"
  /mahkemeKararIslem/mahkemeKararIslemBilgisi:
    post:
      tags:
      - mahkeme-karar-islem-controller
      operationId: mahkemeKararTalepBilgisi_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MahkemeKararTalepBilgisiRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/MahkemeKararTalepQueryResponse"
  /iletisiminTespiti/mahkemeKararSorgu:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: mahkemeKararTalepSorgu_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararSorgulamaResponse"
  /iletisiminTespiti/kararAtama:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: mahkemeKararAtama
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MahkemeKararTalepByKararIdRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/MahkemeKararTalepQueryResponse"
  /iletisiminTespiti/islenecekKararListesi:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: islenecekKararListesi
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararIslenecekRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararSorgulamaResponse"
  /iletisiminTespiti/evrakDurumSorgusu:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: evrakDurumSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDEvrakDurumSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDEvrakDurumSorgulamaResponse"
  /iletisiminDenetlenmesiBtk/islenecekIDEvrakListele:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: islenecekIDEvrakListele
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDIslenecekEvrakListesiRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDIslenecekEvrakListesiResponse"
  /iletisiminDenetlenmesiBtk/idMahkemeKararSorgu:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idMahkemeKararSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararSorgulamaResponse"
  /iletisiminDenetlenmesiBtk/idEvrakTanimlama:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakTanimlama
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararAtamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararOnaylamaResponse"
  /iletisiminDenetlenmesiBtk/idEvrakOnaylama:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakOnaylama
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararAtamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararOnaylamaResponse"
  /iletisiminDenetlenmesiBtk/idEvrakAtama:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakAtama
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDEvrakAtamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDEvrakAtamaResponse"
  /iletisiminDenetlenmesiBtk/idEvrakAtamaKaldir:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakAtamaKaldir
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDEvrakAtamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDEvrakAtamaResponse"
  /iletisiminDenetlenmesiBtk/idEvrakAtamaHistory:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakAtamaHistory
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDEvrakAtamaHistoryRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDEvrakAtamaHistoryResponse"
  /iletisiminDenetlenmesi/mahkemeKararSorgu:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararTalepSorgu_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararSorgulamaResponse"
  /iletisiminDenetlenmesi/mahkemeKararBilgisi:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararTalepBilgisi_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MahkemeKararTalepBilgisiRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/MahkemeKararTalepQueryResponse"
  /iletisiminDenetlenmesi/kararOnaylama:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararOnaylama
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararAtamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararOnaylamaResponse"
  /iletisiminDenetlenmesi/kararAtama:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararAtama_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararAtamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararAtamaResponse"
  /iletisiminDenetlenmesi/islenecekMahkemeKararListele:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: islenecekMahkemeKararListele
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IDMahkemeKararIslenecekRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IDMahkemeKararSorgulamaResponse"
  /htsMahkemeKararTalep/yenikararIT:
    post:
      tags:
      - hts-mahkeme-karar-talep-controller
      operationId: yenikararIT
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  type: string
                  $ref: "#/components/schemas/ITKararRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ITKararResponse"
  /htsMahkemeKararTalep/htsMahkemeKararTalepSorgu:
    post:
      tags:
      - hts-mahkeme-karar-talep-controller
      operationId: htsMahkemeKararTalepSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ITMahkemeKararTalepSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ITMahkemeKararTalepSorgulamaResponse"
  /htsMahkemeKararTalep/htsMahkemeKararTalepBilgisi:
    post:
      tags:
      - hts-mahkeme-karar-talep-controller
      operationId: htsMahkemeKararTalepBilgisi
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MahkemeKararTalepBilgisiRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/MahkemeKararTalepQueryResponse"
  /genelKarar/yeniGenelKarar:
    post:
      tags:
      - genel-evrak-controller
      operationId: yeniGenelEvrak
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                  description: Yuklenecek dosya
                mahkemeKararDetay:
                  type: string
                  $ref: "#/components/schemas/GenelEvrakRequest"
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GenelKararResponse"
  /genelKarar/htsMahkemeKararTalepSorgu:
    post:
      tags:
      - genel-evrak-controller
      operationId: htsMahkemeKararTalepSorgu_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GenelEvrakSorgulamaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GenelEvrakSorgulamaResponse"
  /evrak/islenecekEvrakListesi:
    post:
      tags:
      - evrak-controller
      operationId: islenecekKararListele
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IslenecekEvrakRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IslenecekEvrakResponse"
  /evrak/evrakDurumGuncelle:
    post:
      tags:
      - evrak-controller
      operationId: evrakDurumGuncelle
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EvrakDurumGuncelleRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/EvrakDurumGuncelleResponse"
  /auth/register:
    post:
      tags:
      - auth-controller
      operationId: register
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RegisterRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/RegisterResponse"
  /auth/login:
    post:
      tags:
      - auth-controller
      operationId: login
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoginRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/LoginResponse"
  /auth/changePassword:
    post:
      tags:
      - auth-controller
      operationId: changePassword
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangePasswordRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ChangePasswordResponse"
  /user:
    get:
      tags:
      - user-controller
      operationId: getUser
      parameters:
      - name: username
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GetUserResponse"
  /user/{id}:
    get:
      tags:
      - user-controller
      operationId: getUserById
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GetUserByIdResponse"
  /user/getUsersForAdmin:
    get:
      tags:
      - user-controller
      operationId: getUsersForAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/UsersListResponse"
  /user/getAllUsers:
    get:
      tags:
      - user-controller
      operationId: getAllUsers
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/UsersListResponse"
  /iymDomainSorgu/tespitTurleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: tespitTurleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/TespitTuruListResponse"
  /iymDomainSorgu/sucTipleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: sucTipleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/SucTipleriResponse"
  /iymDomainSorgu/sorguTipleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: sorguTipleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/SorguTipiListResponse"
  /iymDomainSorgu/mahkemeKodlari:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: mahkemeKodlari
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/MahkemeKodlariResponse"
  /iymDomainSorgu/mahkemeKararTipleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: mahkemeKararTipleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/MahkemeKararTipleriResponse"
  /iymDomainSorgu/kurumlar:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: kurumlar
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/EvrakGelenKurumlarResponse"
  /iymDomainSorgu/iller:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: iller
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/IllerResponse"
  /iymDomainSorgu/aidiyatlar:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: aidiyatlar
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/AidiyatResponse"
  /check/healthCheck:
    get:
      tags:
      - health-check-controller
      operationId: healthCheck
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/HealthCheckResponse"
  /check/healthCheckQueryAdmin:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckQueryAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/HealthCheckResponse"
  /check/healthCheckAuthorized:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckAuthorized
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/HealthCheckResponse"
  /check/healthCheckAdmin:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/HealthCheckResponse"
  /audit/getMakosUserAuditLogsByUsername:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogsByUsername
      parameters:
      - name: username
        in: query
        required: true
        schema:
          type: string
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GetMakosUserAuditLogsByUsernameResponse"
  /audit/getMakosUserAuditLogsByType:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogsByType
      parameters:
      - name: auditType
        in: query
        required: true
        schema:
          type: string
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GetMakosUserAuditLogsByTypeResponse"
  /audit/getMakosUserAuditLogList:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogList
      parameters:
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GetMakosUserAuditLogListResponse"
  /audit/getMakosUserAuditLog/{id}:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLog
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/GetMakosUserAuditLogResponse"
components:
  schemas:
    MakosUser:
      type: object
      properties:
        id:
          type: integer
          format: int64
        username:
          type: string
          maxLength: 100
          minLength: 4
        status:
          type: string
          enum:
          - PASSIVE
          - ACTIVE
          - LOCKED
        role:
          type: string
          description: MAKOS kullanıcı rol türleri
          enum:
          - ROLE_ADMIN
          - ROLE_QUERY_ADMIN
          - ROLE_KURUM_TEMSILCISI
          - ROLE_KURUM_KULLANICI
        kurum:
          type: string
          description: Kullanıcı kurumu
          enum:
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - ADLI
          - EMNIYET_SIBER
          - IDB
        newPassword:
          type: string
      required:
      - role
      - status
      - username
    UpdateUserRequest:
      type: object
      properties:
        id:
          type: integer
          format: int64
        user:
          $ref: "#/components/schemas/MakosUser"
      required:
      - id
      - user
    ApiResponse:
      type: object
      properties:
        responseCode:
          type: string
          enum:
          - SUCCESS
          - INVALID_REQUEST
          - FAILED
        responseMessage:
          type: string
      required:
      - responseCode
    UpdateUserResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        id:
          type: integer
          format: int64
      required:
      - response
    DeleteUserRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
      required:
      - userId
    DeleteUserResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
      required:
      - response
    UsernameRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 1
      required:
      - username
    DeactivateUserResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
      required:
      - response
    AddUserRequest:
      type: object
      properties:
        id:
          type: integer
          format: int64
        user:
          $ref: "#/components/schemas/MakosUser"
      required:
      - id
      - user
    AddUserResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        id:
          type: integer
          format: int64
      required:
      - response
    ActivateUserResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
      required:
      - response
    EvrakDetay:
      type: object
      properties:
        evrakNo:
          type: string
          maxLength: 50
          minLength: 0
        evrakTarihi:
          type: string
          format: date-time
        evrakKurumKodu:
          type: string
        evrakTuru:
          type: string
          description: Evrak türü
          enum:
          - ILETISIMIN_TESPITI
          - ILETISIMIN_DENETLENMESI
          - GENEL_EVRAK
        havaleBirimi:
          type: string
          maxLength: 10
          minLength: 0
        aciklama:
          type: string
        geldigiIlIlceKodu:
          type: string
        acilmi:
          type: boolean
        evrakKonusu:
          type: string
      required:
      - evrakKurumKodu
      - evrakNo
      - evrakTarihi
      - evrakTuru
      - geldigiIlIlceKodu
    Hedef:
      type: object
      properties:
        hedefNo:
          type: string
        hedefTip:
          type: string
          description: Hedef tipi enumu
          enum:
          - GSM
          - SABIT
          - UYDU
          - YURT_DISI
          - UMTH_MSISDN
          - UMTH_USERNAME
          - UMTH_IP
          - UMTH_PINCODE
          - EPOSTA
          - IP_TAKIP
          - URL_WEB_ADRESI_TAKIP
          - ADSL_ABONE_TAKIP
          - GPRS
          - IP_ENGELLEME
          - DOMAIN_ENGELLEME
          - IMEI
          - IMSI
          - GPRS_IMSI
          - XDSL_MSISDN
          - XDSL_TEMOSNO
          - XDSL_USERNAME
          - XDSL_IP
          - GPRS_GSM
          - GPRS_IMEI
          - GPRS_YURT_DISI
          - TRUNK
          - GSM_YER_TESPITI
          - GSM_YER_TESPITI_SONLANDIRMA
          - YURTDISI_YER_TESPITI
          - YURTDISI_YER_TESPITI_SONLANDIRMA
      required:
      - hedefNo
      - hedefTip
    HedefWithAdSoyad:
      type: object
      properties:
        hedef:
          $ref: "#/components/schemas/Hedef"
        hedefAd:
          type: string
        hedefSoyad:
          type: string
        tcKimlikNo:
          type: string
        acilmi:
          type: boolean
      required:
      - hedef
      - hedefAd
      - hedefSoyad
    IDHedefDetay:
      type: object
      properties:
        hedefNoAdSoyad:
          $ref: "#/components/schemas/HedefWithAdSoyad"
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          description: Süre tipi enumu
          enum:
          - GUN
          - AY
          - HICBIRI
        sure:
          type: integer
          format: int32
        ilgiliMahkemeKararDetayi:
          $ref: "#/components/schemas/MahkemeKararDetay"
          description: Uzatilan/Sonlandirilan Hedefin ilgili Mahkeme Kararı. Sadece
            uzatma/sonlandirma kararlarinda gerekli
        uzatmaSayisi:
          type: integer
          format: int32
          description: Uzatma Sayisi. Sadece uzatma kararlarinda gerekli
        hedefAidiyatKodlari:
          type: array
          items:
            type: string
        canakNo:
          type: string
          description: Canak numarası. Sadece yeni kararda girilebilir. Zorunlu olmayan
            alan
      required:
      - baslamaTarihi
      - hedefNoAdSoyad
      - sure
      - sureTip
    IDYeniKararRequest:
      type: object
      description: ID Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        yeniKararRequestDetay:
          $ref: "#/components/schemas/IDYeniKararRequestDetay"
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - yeniKararRequestDetay
    IDYeniKararRequestDetay:
      type: object
      properties:
        hedefDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDHedefDetay"
          maxItems: 2147483647
          minItems: 1
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      required:
      - hedefDetayListesi
    MahkemeKararBilgisi:
      type: object
      properties:
        mahkemeKararTipi:
          type: string
          description: Mahkeme karar tipi
          enum:
          - ONLEYICI_HAKIM_KARARI
          - SINYAL_BILGI_DEGERLENDIRME_KARARI
          - ABONE_KUTUK_BILGILERI_KARARI
          - ONLEYICI_YAZILI_EMIR
          - ADLI_HAKIM_KARARI
          - ADLI_HAKIM_HTS_KARARI
          - ADLI_YAZILI_EMIR
          - ADLI_KHK_YAZILI_EMIR
          - ADLI_SAVCILIK_HTS_KARARI
          - HEDEF_AD_SOYAD_DEGISTIRME
          - HEDEF_BILGI_DEGISTIRME
          - MAHKEME_KODU_DEGISTIRME
          - MAHKEME_KARAR_BILGI_DEGISTIRME
          - MAHKEME_AIDIYAT_DEGISTIRME
          - ONLEYICI_SONLANDIRMA
          - ADLI_SONLANDIRMA
          - ADLI_SAVCILIK_SONLANDIRMA
          - ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA
          - ADLI_KHK_SONLANDIRMA
          - ADLI_ASKERI_HAKIM_KARARI
          - ADLI_ASKERI_SONLANDIRMA
          - ADLI_ASKERI_SAVCILIK_SONLANDIRMA
          - CANAK_NUMARA_DEGISTIRME
          - ADLI_ASKERI_YER_TESPITI_SONLANDIRMA
          - MAHKEME_SUCTIPI_DEGISTIRME
        mahkemeKararDetay:
          $ref: "#/components/schemas/MahkemeKararDetay"
      required:
      - mahkemeKararDetay
      - mahkemeKararTipi
    MahkemeKararDetay:
      type: object
      properties:
        mahkemeKodu:
          type: string
        mahkemeIlIlceKodu:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
        aciklama:
          type: string
      required:
      - mahkemeIlIlceKodu
      - mahkemeKodu
    IDYeniKararResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    MakosApiResponse:
      type: object
      properties:
        responseCode:
          type: string
          enum:
          - SUCCESS
          - INVALID_REQUEST
          - FAILED
        responseMessage:
          type: string
      required:
      - responseCode
    IDUzatmaKarariRequest:
      type: object
      description: ID Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        uzatmaKarariRequestDetay:
          $ref: "#/components/schemas/IDUzatmaKarariRequestDetay"
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - uzatmaKarariRequestDetay
    IDUzatmaKarariRequestDetay:
      type: object
      properties:
        hedefDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDHedefDetay"
          maxItems: 2147483647
          minItems: 1
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      required:
      - hedefDetayListesi
    IDUzatmaKarariResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    IDSucTipiGuncellemeRequest:
      type: object
      description: Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        sucTipiGuncellemeRequestDetay:
          $ref: "#/components/schemas/IDSucTipiGuncellemeRequestDetay"
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - sucTipiGuncellemeRequestDetay
    IDSucTipiGuncellemeRequestDetay:
      type: object
      properties:
        sucTipiGuncellemeKararDetayListesi:
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
          items:
            $ref: "#/components/schemas/SucTipiGuncellemeKararDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - sucTipiGuncellemeKararDetayListesi
    SucTipiGuncellemeDetay:
      type: object
      properties:
        guncellemeTip:
          type: string
          description: Güncelleme tipi enumu
          enum:
          - EKLE
          - CIKAR
        sucTipiKodu:
          type: string
      required:
      - guncellemeTip
      - sucTipiKodu
    SucTipiGuncellemeKararDetay:
      type: object
      properties:
        mahkemeKararDetay:
          $ref: "#/components/schemas/MahkemeKararDetay"
          description: Suç tipi değişikliği yapılacak mahkeme karar bilgileri
        sucTipiGuncellemeDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/SucTipiGuncellemeDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - mahkemeKararDetay
      - sucTipiGuncellemeDetayListesi
    IDSucTipiGuncellemeResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    IDSonlandirmaKarariRequest:
      type: object
      description: ID Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        sonlandirmaKarariRequestDetay:
          $ref: "#/components/schemas/IDSonlandirmaKarariRequestDetay"
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - sonlandirmaKarariRequestDetay
    IDSonlandirmaKarariRequestDetay:
      type: object
      properties:
        hedefDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDHedefDetay"
          maxItems: 2147483647
          minItems: 1
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      required:
      - hedefDetayListesi
    IDSonlandirmaKarariResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    IDMahkemeKararTalepSorgulamaRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sorguParam:
          $ref: "#/components/schemas/MahkemeKararTalepSorguParam"
      required:
      - id
      - sorguParam
    MahkemeKararTalepSorguParam:
      type: object
      properties:
        sorusturmaNo:
          type: string
        mahkemeKararNo:
          type: string
        mahkemeKodu:
          type: string
        durum:
          type: string
        aciklama:
          type: string
        kayitTarihi:
          type: string
          format: date-time
        kaydedenKullaniciId:
          type: integer
          format: int64
        evrakSiraNo:
          type: string
    IDMahkemeKararTalepSorgulamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        mahkemeKararTalepSorguViewListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararTalepSorguView"
      required:
      - requestId
      - response
    MahkemeKararTalepSorguView:
      type: object
      description: Mahkeme Karar Talep Listeleme  Bilgisi
      properties:
        mahkemeKararTalepId:
          type: integer
          format: int64
          description: Mahkeme Karar Talep Id
        evrakId:
          type: integer
          format: int64
          description: Evrak Id
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        evrakNo:
          type: string
          description: Evrak No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        kurumEvrakTarihi:
          type: string
          format: date-time
          description: Kurum Evrak Tarihi
        kararKayitTarihi:
          type: string
          format: date-time
          description: Karar Kayıt Tarihi
        mahkemeIlIlceKodu:
          type: string
          description: Mahkeme İl/İlçe Kodu
        mahkemeIlIlceAdi:
          type: string
          description: Mahkeme İl/İlçe Adı
        kaydedenKullaniciId:
          type: integer
          format: int64
          description: Kaydeden Kullanıcı Id
        kaydedenKullaniciAdi:
          type: string
          description: Kaydeden Kullanıcı Adı
        kaydedenAdiSoyadi:
          type: string
          description: Kaydeden Adı/Soyadı
        adi:
          type: string
          description: Adı
        soyadi:
          type: string
          description: Soyadı
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        aciklama:
          type: string
          description: Açıklama
        durumu:
          type: string
          description: Durumu
        mahkemeKodu:
          type: string
          description: Mahkeme Kodu
        mahkemeAdi:
          type: string
          description: Mahkeme Adı
        kurumKodu:
          type: string
          description: Kurum Kodu
        kurumAdi:
          type: string
          description: Kurum Adı
        evrakKonusu:
          type: string
          description: Evrak Konusu
    MahkemeKararTalepBilgisiRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        mahkemeKararId:
          type: integer
          format: int64
      required:
      - id
      - mahkemeKararId
    DetayMahkemeKararInfo:
      type: object
      properties:
        mahkemeKararId:
          type: integer
          format: int64
        mahkemeKodu:
          type: string
        mahkemeAdi:
          type: string
        mahkemeIlIlceKodu:
          type: string
        ilIlceAdi:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
    EvrakDetayInfo:
      type: object
      properties:
        evrakNo:
          type: string
        evrakTarihi:
          type: string
          format: date-time
        evrakKurumKodu:
          type: string
        evrakKurumAdi:
          type: string
        evrakTuru:
          type: string
          description: Evrak türü
          enum:
          - ILETISIMIN_TESPITI
          - ILETISIMIN_DENETLENMESI
          - GENEL_EVRAK
        geldigiIlIlceKodu:
          type: string
        geldigiIlIlceAdi:
          type: string
        acilmi:
          type: boolean
        evrakKonusu:
          type: string
        aciklama:
          type: string
        durumu:
          type: string
    HedefGuncellemeAlanInfo:
      type: object
      properties:
        hedefGuncellemeAlanTuru:
          type: string
          enum:
          - AD
          - SOYAD
          - TCKIMlIKNO
          - CANAK_NO
        yeniDegeri:
          type: string
      required:
      - hedefGuncellemeAlanTuru
    HedefGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: "#/components/schemas/DetayMahkemeKararInfo"
        hedeflerDetayList:
          type: array
          items:
            $ref: "#/components/schemas/HedeflerDetayDTO"
    HedeflerDetayDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        hedefNo:
          type: string
        hedefTipi:
          type: string
          description: Hedef tipi enumu
          enum:
          - GSM
          - SABIT
          - UYDU
          - YURT_DISI
          - UMTH_MSISDN
          - UMTH_USERNAME
          - UMTH_IP
          - UMTH_PINCODE
          - EPOSTA
          - IP_TAKIP
          - URL_WEB_ADRESI_TAKIP
          - ADSL_ABONE_TAKIP
          - GPRS
          - IP_ENGELLEME
          - DOMAIN_ENGELLEME
          - IMEI
          - IMSI
          - GPRS_IMSI
          - XDSL_MSISDN
          - XDSL_TEMOSNO
          - XDSL_USERNAME
          - XDSL_IP
          - GPRS_GSM
          - GPRS_IMEI
          - GPRS_YURT_DISI
          - TRUNK
          - GSM_YER_TESPITI
          - GSM_YER_TESPITI_SONLANDIRMA
          - YURTDISI_YER_TESPITI
          - YURTDISI_YER_TESPITI_SONLANDIRMA
        hedefAdi:
          type: string
        hedefSoyadi:
          type: string
        mahkemeKararId:
          type: integer
          format: int64
        detayMahkemeKararId:
          type: integer
          format: int64
        iliskiliHedefId:
          type: integer
          format: int64
        kayitTarihi:
          type: string
          format: date-time
        durumu:
          type: string
        canakNo:
          type: string
        tcKimlikNo:
          type: string
        updateColumnNames:
          type: string
        adiGuncellemeAlani:
          $ref: "#/components/schemas/HedefGuncellemeAlanInfo"
        soyadiGuncellemeAlani:
          $ref: "#/components/schemas/HedefGuncellemeAlanInfo"
        tcknGuncellemeAlani:
          $ref: "#/components/schemas/HedefGuncellemeAlanInfo"
        canakNoGuncellemeAlani:
          $ref: "#/components/schemas/HedefGuncellemeAlanInfo"
    IDAidiyatGuncellemeKararDetay:
      type: object
      properties:
        aidiyatGuncellemeListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararAidiyatGuncellemeInfo"
    IDHedefAidiyatInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        hedefId:
          type: integer
          format: int64
        aidiyatKod:
          type: string
        tarih:
          type: string
          format: date-time
        durumu:
          type: string
    IDHedefGuncellemeKararDetay:
      type: object
      properties:
        guncellemeListesi:
          type: array
          items:
            $ref: "#/components/schemas/HedefGuncellemeInfo"
    IDHedefInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        hedefNo:
          type: string
        hedefTip:
          type: string
          description: Hedef tipi enumu
          enum:
          - GSM
          - SABIT
          - UYDU
          - YURT_DISI
          - UMTH_MSISDN
          - UMTH_USERNAME
          - UMTH_IP
          - UMTH_PINCODE
          - EPOSTA
          - IP_TAKIP
          - URL_WEB_ADRESI_TAKIP
          - ADSL_ABONE_TAKIP
          - GPRS
          - IP_ENGELLEME
          - DOMAIN_ENGELLEME
          - IMEI
          - IMSI
          - GPRS_IMSI
          - XDSL_MSISDN
          - XDSL_TEMOSNO
          - XDSL_USERNAME
          - XDSL_IP
          - GPRS_GSM
          - GPRS_IMEI
          - GPRS_YURT_DISI
          - TRUNK
          - GSM_YER_TESPITI
          - GSM_YER_TESPITI_SONLANDIRMA
          - YURTDISI_YER_TESPITI
          - YURTDISI_YER_TESPITI_SONLANDIRMA
        hedefAdi:
          type: string
        hedefSoyadi:
          type: string
        tcKimlikNo:
          type: string
        durumu:
          type: string
        acilmi:
          type: string
        aciklama:
          type: string
        kayitTarihi:
          type: string
          format: date-time
        tanimlamaTarihi:
          type: string
          format: date-time
    IDKararSucTipiGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: "#/components/schemas/DetayMahkemeKararInfo"
        sucTipiGuncellemeListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeSucTipiGuncellemeDTO"
    IDKarariAidiyatInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        aidiyatKod:
          type: string
        durumu:
          type: string
    IDKarariSucTipiInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        sucTipiKodu:
          type: string
        sucTipi:
          $ref: "#/components/schemas/SucTipiInfo"
        durumu:
          type: string
    IDMahkemeBilgiGuncellemeKararDetay:
      type: object
      properties:
        guncellemeListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararBilgisiGuncellemeInfo"
    IDSonlandirmaKararDetay:
      type: object
      properties:
        hedefListesi:
          type: array
          items:
            $ref: "#/components/schemas/SonlandirmaKarariHedefInfo"
        aidiyatListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDKarariAidiyatInfo"
        sucTipleri:
          type: array
          items:
            $ref: "#/components/schemas/IDKarariSucTipiInfo"
    IDSucTipiGuncellemeKararDetay:
      type: object
      properties:
        sucTipiGuncellemeListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDKararSucTipiGuncellemeInfo"
    IDUzatmaKararDetay:
      type: object
      properties:
        hedefListesi:
          type: array
          items:
            $ref: "#/components/schemas/UzatmaKarariHedefInfo"
        aidiyatListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDKarariAidiyatInfo"
        sucTipleri:
          type: array
          items:
            $ref: "#/components/schemas/IDKarariSucTipiInfo"
    IDYeniKararDetay:
      type: object
      properties:
        hedefListesi:
          type: array
          items:
            $ref: "#/components/schemas/YeniIDKarariHedefInfo"
        mahkemeAidiyatlari:
          type: array
          items:
            $ref: "#/components/schemas/IDKarariAidiyatInfo"
        sucTipleri:
          type: array
          items:
            $ref: "#/components/schemas/IDKarariSucTipiInfo"
    ITKarariHedefInfo:
      type: object
      properties:
        hedefNo:
          type: string
        hedefTip:
          type: string
          description: Hedef tipi enumu
          enum:
          - GSM
          - SABIT
          - UYDU
          - YURT_DISI
          - UMTH_MSISDN
          - UMTH_USERNAME
          - UMTH_IP
          - UMTH_PINCODE
          - EPOSTA
          - IP_TAKIP
          - URL_WEB_ADRESI_TAKIP
          - ADSL_ABONE_TAKIP
          - GPRS
          - IP_ENGELLEME
          - DOMAIN_ENGELLEME
          - IMEI
          - IMSI
          - GPRS_IMSI
          - XDSL_MSISDN
          - XDSL_TEMOSNO
          - XDSL_USERNAME
          - XDSL_IP
          - GPRS_GSM
          - GPRS_IMEI
          - GPRS_YURT_DISI
          - TRUNK
          - GSM_YER_TESPITI
          - GSM_YER_TESPITI_SONLANDIRMA
          - YURTDISI_YER_TESPITI
          - YURTDISI_YER_TESPITI_SONLANDIRMA
        hedefAdi:
          type: string
        hedefSoyadi:
          type: string
    ITYeniKararDetay:
      type: object
      properties:
        hedefListesi:
          type: array
          items:
            $ref: "#/components/schemas/ITKarariHedefInfo"
    MahkemeAidiyatDetayDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        iliskiliMahkemeKararId:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        mahkemeAidiyatKoduEkle:
          type: string
        mahkemeAidiyatKoduCikar:
          type: string
        tarih:
          type: string
          format: date-time
        durum:
          type: string
        detayMahkemeKararId:
          type: integer
          format: int64
    MahkemeKararAidiyatGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: "#/components/schemas/DetayMahkemeKararInfo"
        aidiyatGuncellemeListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeAidiyatDetayDTO"
    MahkemeKararBilgisiGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: "#/components/schemas/DetayMahkemeKararInfo"
        mahkemeKararGuncellemeDTO:
          $ref: "#/components/schemas/MahkemeKararGuncellemeDTO"
    MahkemeKararBilgisiInfo:
      type: object
      properties:
        mahkemeKodu:
          type: string
        mahkemeIlIlceKodu:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
        kararBaslamaTarihi:
          type: string
          format: date-time
        kararBitisTarihi:
          type: string
          format: date-time
        aciklama:
          type: string
        durumu:
          type: string
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
    MahkemeKararGuncellemeAlanInfo:
      type: object
      properties:
        mahkemeKararGuncellemeAlanTuru:
          type: string
          enum:
          - MAHKEME_KODU
          - SORUSTURMA_NO
          - MAHKEME_KARAR_NO
        yeniDegeri:
          type: string
      required:
      - mahkemeKararGuncellemeAlanTuru
    MahkemeKararGuncellemeDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        detayMahkemeKararId:
          type: integer
          format: int64
        durumu:
          type: string
        guncellemeAlanListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararGuncellemeAlanInfo"
    MahkemeKararTalepQueryResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        idmahkemeKarariInfo:
          $ref: "#/components/schemas/MahkemeKarariInfo"
      required:
      - requestId
      - response
    MahkemeKarariInfo:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        mahkemeKararId:
          type: integer
          format: int64
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetayInfo"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisiInfo"
        dosyaAdi:
          type: string
        durumu:
          type: string
        idsonlandirmaKararDetay:
          $ref: "#/components/schemas/IDSonlandirmaKararDetay"
        idaidiyatGuncellemeKararDetay:
          $ref: "#/components/schemas/IDAidiyatGuncellemeKararDetay"
        idhedefGuncellemeKararDetay:
          $ref: "#/components/schemas/IDHedefGuncellemeKararDetay"
        idsucTipiGuncellemeKararDetay:
          $ref: "#/components/schemas/IDSucTipiGuncellemeKararDetay"
        iduzatmaKararDetay:
          $ref: "#/components/schemas/IDUzatmaKararDetay"
        idyeniKararDetay:
          $ref: "#/components/schemas/IDYeniKararDetay"
        idmahkemeBilgiGuncellemeKararDetay:
          $ref: "#/components/schemas/IDMahkemeBilgiGuncellemeKararDetay"
        ityeniKararDetay:
          $ref: "#/components/schemas/ITYeniKararDetay"
    MahkemeSucTipiGuncellemeDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        iliskiliMahkemeKararId:
          type: integer
          format: int64
        detayMahkemeKararId:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        mahkemeSucTipiKoduEkle:
          type: string
        mahkemeSucTipiKoduCikar:
          type: string
        tarih:
          type: string
          format: date-time
        durum:
          type: string
    SonlandirmaKarariHedefInfo:
      type: object
      properties:
        hedefBilgisi:
          $ref: "#/components/schemas/IDHedefInfo"
        iliskiliMahkemeKararInfo:
          $ref: "#/components/schemas/DetayMahkemeKararInfo"
        kapatmaTarihi:
          type: string
          format: date-time
        kapatmaMahkemeKararId:
          type: integer
          format: int64
        baslamaTarihi:
          type: string
          format: date-time
        hedefAidiyatListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDHedefAidiyatInfo"
    SucTipiInfo:
      type: object
      properties:
        sucTipiKodu:
          type: string
        sucTipAdi:
          type: string
    UzatmaKarariHedefInfo:
      type: object
      properties:
        hedefBilgisi:
          $ref: "#/components/schemas/IDHedefInfo"
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          description: Süre tipi enumu
          enum:
          - GUN
          - AY
          - HICBIRI
        sure:
          type: integer
          format: int32
        uzatmaSayisi:
          type: integer
          format: int32
        iliskiliMahkemeKararInfo:
          $ref: "#/components/schemas/DetayMahkemeKararInfo"
        hedefAidiyatListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDHedefAidiyatInfo"
        sucTipleri:
          type: array
          items:
            $ref: "#/components/schemas/IDKarariSucTipiInfo"
        canakNo:
          type: string
    YeniIDKarariHedefInfo:
      type: object
      properties:
        hedefBilgileri:
          $ref: "#/components/schemas/IDHedefInfo"
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          description: Süre tipi enumu
          enum:
          - GUN
          - AY
          - HICBIRI
        sure:
          type: integer
          format: int32
        hedefAidiyatListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDHedefAidiyatInfo"
        canakNo:
          type: string
    IDMahkemeKararGuncellemeRequest:
      type: object
      description: Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        mahkemeKararGuncellemeRequestDetay:
          $ref: "#/components/schemas/IDMahkemeKararGuncellemeRequestDetay"
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            yeni kod/il bilgileri
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - mahkemeKararGuncellemeRequestDetay
    IDMahkemeKararGuncellemeRequestDetay:
      type: object
      properties:
        mahkemeKararGuncellemeDetayListesi:
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            yeni kod/il bilgileri
          items:
            $ref: "#/components/schemas/MahkemeKararGuncellemeDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - mahkemeKararGuncellemeDetayListesi
    MahkemeKararGuncellemeBilgi:
      type: object
      properties:
        mahkemeKararGuncellemeAlanTuru:
          type: string
          enum:
          - MAHKEME_KODU
          - SORUSTURMA_NO
          - MAHKEME_KARAR_NO
        yeniDegeri:
          type: string
      required:
      - mahkemeKararGuncellemeAlanTuru
    MahkemeKararGuncellemeDetay:
      type: object
      properties:
        mahkemeKararDetay:
          $ref: "#/components/schemas/MahkemeKararDetay"
          description: Mahkeme karar bilgisi değişikliği yapılacak olan mahkeme karar
            bilgileri
        mahkemeKararGuncellemeBilgiListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararGuncellemeBilgi"
          maxItems: 2147483647
          minItems: 1
      required:
      - mahkemeKararDetay
      - mahkemeKararGuncellemeBilgiListesi
    IDMahkemeKararGuncellemeResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    HedefGuncellemeBilgi:
      type: object
      properties:
        hedefGuncellemeAlan:
          type: string
          enum:
          - AD
          - SOYAD
          - TCKIMlIKNO
          - CANAK_NO
        yeniDegeri:
          type: string
      required:
      - hedefGuncellemeAlan
    HedefGuncellemeDetay:
      type: object
      properties:
        hedef:
          $ref: "#/components/schemas/Hedef"
          description: Değişiklik yapılacak hedefin hedefNo ve hedefTip bilgileri
        hedefGuncellemeBilgiListesi:
          type: array
          items:
            $ref: "#/components/schemas/HedefGuncellemeBilgi"
          maxItems: 2147483647
          minItems: 1
      required:
      - hedef
      - hedefGuncellemeBilgiListesi
    HedefGuncellemeKararDetay:
      type: object
      properties:
        mahkemeKararDetay:
          $ref: "#/components/schemas/MahkemeKararDetay"
          description: Değişiklik yapılacak hedefin daha önce gönderilen mahkeme karar
            bilgileri
        hedefGuncellemeDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/HedefGuncellemeDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - hedefGuncellemeDetayListesi
      - mahkemeKararDetay
    IDHedefGuncellemeRequest:
      type: object
      description: Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        hedefGuncellemeRequestDetay:
          $ref: "#/components/schemas/IDHedefGuncellemeRequestDetay"
          description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve\
            \ karara ait güncellenecek ad, soyad bilgileri"
      required:
      - evrakDetay
      - hedefGuncellemeRequestDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
    IDHedefGuncellemeRequestDetay:
      type: object
      properties:
        hedefGuncellemeKararDetayListesi:
          type: array
          description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve\
            \ karara ait güncellenecek ad, soyad bilgileri"
          items:
            $ref: "#/components/schemas/HedefGuncellemeKararDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - hedefGuncellemeKararDetayListesi
    IDHedefGuncellemeResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    AidiyatGuncellemeDetay:
      type: object
      properties:
        guncellemeTip:
          type: string
          description: Güncelleme tipi enumu
          enum:
          - EKLE
          - CIKAR
        aidiyatKodu:
          type: string
      required:
      - aidiyatKodu
      - guncellemeTip
    AidiyatGuncellemeKararDetay:
      type: object
      properties:
        mahkemeKararDetay:
          $ref: "#/components/schemas/MahkemeKararDetay"
          description: Aidiyat değişikliği yapılacak mahkeme karar bilgileri
        aidiyatGuncellemeDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/AidiyatGuncellemeDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - aidiyatGuncellemeDetayListesi
      - mahkemeKararDetay
    IDAidiyatBilgisiGuncellemeRequest:
      type: object
      description: Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        idAidiyatBilgisiGuncellemeRequestDetay:
          $ref: "#/components/schemas/IDAidiyatBilgisiGuncellemeRequestDetay"
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
      required:
      - evrakDetay
      - id
      - idAidiyatBilgisiGuncellemeRequestDetay
      - kararTuru
      - mahkemeKararBilgisi
    IDAidiyatBilgisiGuncellemeRequestDetay:
      type: object
      properties:
        aidiyatGuncellemeKararDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/AidiyatGuncellemeKararDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - aidiyatGuncellemeKararDetayListesi
    IDAidiyatBilgisiGuncellemeResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    IDMahkemeKararSorgulamaRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sorguParam:
          $ref: "#/components/schemas/MahkemeKararSorguParam"
      required:
      - id
      - sorguParam
    MahkemeKararSorguParam:
      type: object
      properties:
        evrakSiraNo:
          type: string
        evrakNo:
          type: string
        sorusturmaNo:
          type: string
        mahkemeKararNo:
          type: string
        mahkemeKodu:
          type: string
        evrakIlKodu:
          type: string
        mahkemeIlKodu:
          type: string
        durum:
          type: string
        aciklama:
          type: string
        kayitTarihi:
          type: string
          format: date-time
    IDMahkemeKararSorgulamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        kararlar:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararSorguView"
      required:
      - requestId
      - response
    MahkemeKararSorguView:
      type: object
      description: Mahkeme Karar Talep Listeleme  Bilgisi
      properties:
        mahkemeKararTalepId:
          type: integer
          format: int64
          description: Mahkeme Karar Talep Id
        evrakId:
          type: integer
          format: int64
          description: Evrak Id
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        kurumEvrakTarihi:
          type: string
          format: date-time
          description: Kurum Evrak Tarihi
        kararKayitTarihi:
          type: string
          format: date-time
          description: Kurum Evrak No
        mahkemeIlIlceKodu:
          type: string
          description: Mahkeme İl/İlçe Kodu
        mahkemeIlIlceAdi:
          type: string
          description: Mahkeme İl/İlçe Adı
        kaydedenKullaniciId:
          type: integer
          format: int64
          description: Kaydeden Kullanıcı Id
        kaydedenKullaniciAdi:
          type: string
          description: Kaydeden KullanıcıAdi
        kaydedenAdiSoyadi:
          type: string
          description: Kaydeden Adı/Soyadı
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        aciklama:
          type: string
          description: Açıklama
        durumu:
          type: string
          description: Durumu
    MahkemeKararTalepByKararIdRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        mahkemeKararTalepIslemId:
          type: integer
          format: int64
      required:
      - id
      - mahkemeKararTalepIslemId
    IDMahkemeKararIslenecekRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
      required:
      - id
    IDEvrakDurumSorgulamaRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        kurumEvrakNo:
          type: string
        evrakSiraNo:
          type: string
        evrakId:
          type: integer
          format: int64
      required:
      - id
    IDEvrakDurumSorgulamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakListesi:
          type: array
          items:
            $ref: "#/components/schemas/IdEvrakDurumSorguView"
      required:
      - requestId
      - response
    IdEvrakDurumSorguView:
      type: object
      description: 'ID '
      properties:
        evrakId:
          type: integer
          format: int64
          description: Evrak Id
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        girisTarihi:
          type: string
          format: date-time
          description: Evrak Giriş Tarihi
        onayTarihi:
          type: string
          format: date-time
          description: Evrak OnayTarihi
        durumu:
          type: string
          description: Durumu
    IDIslenecekEvrakListesiRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        atananKullaniciId:
          type: integer
          format: int64
        gorevTipi:
          type: string
        tanimlama:
          type: boolean
        onaylama:
          type: boolean
        nobetci:
          type: boolean
      required:
      - id
    IDIslenecekEvrakDTO:
      type: object
      description: İşlenecek Denetleme Evrak Bilgisi
      properties:
        evrakId:
          type: integer
          format: int64
          description: Evrak Id
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        evrakNo:
          type: string
          description: Kurum Evrak No
        evrakGirişTarihi:
          type: string
          format: date-time
          description: Evrak Giriş Tarihi
        evrakTarihi:
          type: string
          format: date-time
          description: Kurum Evrak Tarihi
        evrakIlIlceKodu:
          type: string
          description: Evrak İl/İlçe Kodu
        evraklIlceAdi:
          type: string
          description: Evrak İl/İlçe Adı
        evrakKurumKodu:
          type: string
          description: Evrak Kurum Kodu
        evrakKurumAdı:
          type: string
          description: Evrak Kurum Adı
        acil:
          type: boolean
          description: Kurum Evrak No
        aciklama:
          type: string
          description: Açıklama
        mahkemeKararTalepId:
          type: integer
          format: int64
          description: Mahkeme Karar Talep Id
        atayanKullaniciId:
          type: integer
          format: int64
          description: Atayan Kullanıcı Id
        atayanAdiSoyadi:
          type: string
          description: Atayan KullanıcıAdi
        atananKullaniciId:
          type: integer
          format: int64
          description: Atanan Kullanıcı Id
        atananAdiSoyadi:
          type: string
          description: Atanan KullanıcıAdi
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        mahkemeKodu:
          type: string
          description: Mahkeme Kodu
        mahkemeAdi:
          type: string
          description: Mahkeme Adı
    IDIslenecekEvrakListesiResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        islenecekEvrakListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDIslenecekEvrakDTO"
      required:
      - requestId
      - response
    IDMahkemeKararAtamaRequest:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        mahkemeKararAtamaRequestDetay:
          $ref: "#/components/schemas/IDMahkemeKararAtamaRequestDetay"
      required:
      - mahkemeKararAtamaRequestDetay
      - requestId
    IDMahkemeKararAtamaRequestDetay:
      type: object
      properties:
        evrakId:
          type: integer
          format: int64
        atananKullaniciId:
          type: integer
          format: int64
      required:
      - atananKullaniciId
      - evrakId
    IDMahkemeKararOnaylamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        aciklama:
          type: string
      required:
      - evrakId
      - requestId
      - response
    IDEvrakAtamaRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        evrakId:
          type: integer
          format: int64
        atananKullaniciId:
          type: integer
          format: int64
        atayanKullaniciId:
          type: integer
          format: int64
        aciklama:
          type: string
        sebebi:
          type: string
      required:
      - atananKullaniciId
      - atayanKullaniciId
      - evrakId
      - id
      - sebebi
    IDEvrakAtamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        aciklama:
          type: string
      required:
      - evrakId
      - requestId
      - response
    IDEvrakAtamaHistoryRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        evrakId:
          type: integer
          format: int64
      required:
      - evrakId
      - id
    IDEvrakAtamaDTO:
      type: object
      description: Evrak Atama History Bilgisi
      properties:
        evrakId:
          type: integer
          format: int64
        evrakSiraNo:
          type: string
        evrakNo:
          type: string
        sorusturmaNo:
          type: string
        mahkemeKararNo:
          type: string
        atayanKullaniciId:
          type: integer
          format: int64
        atayanAdiSoyadi:
          type: string
        atananKullaniciId:
          type: integer
          format: int64
        atananAdiSoyadi:
          type: string
        aciklama:
          type: string
        sebebi:
          type: string
        durum:
          type: string
    IDEvrakAtamaHistoryResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        atamaListesi:
          type: array
          items:
            $ref: "#/components/schemas/IDEvrakAtamaDTO"
      required:
      - requestId
      - response
    IDMahkemeKararAtamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        aciklama:
          type: string
      required:
      - evrakId
      - requestId
      - response
    ITHedefDetay:
      type: object
      properties:
        sorguTipi:
          type: string
          description: Sorgu tipi enumu
          enum:
          - TELEFON_GORUSME
          - IMEI_GORUSME
          - IMEI_KULLANAN_NUMARA
        hedef:
          $ref: "#/components/schemas/Hedef"
        karsiHedef:
          $ref: "#/components/schemas/Hedef"
        baslamaTarihi:
          type: string
          format: date-time
        bitisTarihi:
          type: string
          format: date-time
        tespitTuru:
          type: string
        tespitTuruDetay:
          type: string
        aciklama:
          type: string
      required:
      - baslamaTarihi
      - bitisTarihi
      - hedef
      - sorguTipi
      - tespitTuru
    ITKararRequest:
      type: object
      description: IT Mahkeme Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
        hedefDetayListesi:
          type: array
          items:
            $ref: "#/components/schemas/ITHedefDetay"
          maxItems: 2147483647
          minItems: 1
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
    ITKararResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        itkTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - itkTalepId
      - requestId
      - response
    ITMahkemeKararTalepSorgulamaRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sorguParam:
          $ref: "#/components/schemas/MahkemeKararTalepSorguParam"
      required:
      - id
      - sorguParam
    ITMahkemeKararTalepSorgulamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        mahkemeKararTalepSorguViewListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararTalepSorguView"
      required:
      - requestId
      - response
    GenelEvrakRequest:
      type: object
      description: Genel Evrak Karar Detaylari
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: "#/components/schemas/EvrakDetay"
        mahkemeKararBilgisi:
          $ref: "#/components/schemas/MahkemeKararBilgisi"
          description: Mahkeme karar bilgileri
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
    GenelKararResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        evrakId:
          type: integer
          format: int64
        mahkemeKararTalepId:
          type: integer
          format: int64
      required:
      - evrakId
      - mahkemeKararTalepId
      - requestId
      - response
    GenelEvrakSorgulamaRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sorguParam:
          $ref: "#/components/schemas/MahkemeKararTalepSorguParam"
      required:
      - id
      - sorguParam
    GenelEvrakSorgulamaResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        mahkemeKararTalepSorguViewListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararTalepSorguView"
      required:
      - requestId
      - response
    IslenecekEvrakRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        page:
          type: integer
          format: int32
        size:
          type: integer
          format: int32
      required:
      - id
    IslenecekEvrakResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        islenecekEvraklar:
          $ref: "#/components/schemas/PageIslenecekEvrakViewInfo"
      required:
      - requestId
      - response
    IslenecekEvrakViewInfo:
      type: object
      description: İşlenecek Evrak Bilgisi
      properties:
        evrakId:
          type: integer
          format: int64
          description: Evrak Id
        evrakTuru:
          type: string
          description: Evrak Türü
          enum:
          - ILETISIMIN_TESPITI
          - ILETISIMIN_DENETLENMESI
          - GENEL_EVRAK
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        kurumEvrakTarihi:
          type: string
          format: date-time
          description: Kurum Evrak Tarihi
        kayitTarihi:
          type: string
          format: date-time
          description: Kayıt Tarihi
        geldigiIlIlceKodu:
          type: string
          description: Mahkeme İl/İlçe Kodu
        geldigiIlIlceAdi:
          type: string
          description: Mahkeme İl/İlçe Adı
        kaydedenKullaniciId:
          type: integer
          format: int64
          description: Kaydeden Kullanıcı Id
        kaydedenKullaniciAdi:
          type: string
          description: Kaydeden KullanıcıAdi
        kaydedenAdiSoyadi:
          type: string
          description: Kaydeden Adı/Soyadı
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        aciklama:
          type: string
          description: Açıklama
        durumu:
          type: string
          description: Durumu
    PageIslenecekEvrakViewInfo:
      type: object
      properties:
        totalElements:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
        first:
          type: boolean
        last:
          type: boolean
        size:
          type: integer
          format: int32
        content:
          type: array
          items:
            $ref: "#/components/schemas/IslenecekEvrakViewInfo"
        number:
          type: integer
          format: int32
        sort:
          $ref: "#/components/schemas/SortObject"
        numberOfElements:
          type: integer
          format: int32
        pageable:
          $ref: "#/components/schemas/PageableObject"
        empty:
          type: boolean
    PageableObject:
      type: object
      properties:
        offset:
          type: integer
          format: int64
        sort:
          $ref: "#/components/schemas/SortObject"
        pageNumber:
          type: integer
          format: int32
        paged:
          type: boolean
        pageSize:
          type: integer
          format: int32
        unpaged:
          type: boolean
    SortObject:
      type: object
      properties:
        empty:
          type: boolean
        unsorted:
          type: boolean
        sorted:
          type: boolean
    EvrakDurumGuncelleRequest:
      type: object
      properties:
        id:
          type: string
          format: uuid
        talepGuncellemeTuru:
          type: string
          description: Talep güncelleme türü enumu
          enum:
          - ONAYLA
          - ARSIV
          - SIL
        evrakId:
          type: integer
          format: int64
      required:
      - evrakId
      - id
      - talepGuncellemeTuru
    EvrakDurumGuncelleResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        evrakId:
          type: integer
          format: int64
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        success:
          type: boolean
      required:
      - evrakId
      - requestId
      - response
    RegisterRequest:
      type: object
      properties:
        userName:
          type: string
          minLength: 1
        password:
          type: string
          minLength: 1
        role:
          type: string
          description: MAKOS kullanıcı rol türleri
          enum:
          - ROLE_ADMIN
          - ROLE_QUERY_ADMIN
          - ROLE_KURUM_TEMSILCISI
          - ROLE_KURUM_KULLANICI
        kurum:
          type: string
          description: Kullanıcı kurumu
          enum:
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - ADLI
          - EMNIYET_SIBER
          - IDB
      required:
      - kurum
      - password
      - role
      - userName
    RegisterResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
      required:
      - response
    LoginRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 1
        password:
          type: string
          minLength: 1
      required:
      - password
      - username
    LoginResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        token:
          type: string
        userId:
          type: integer
          format: int64
        username:
          type: string
        actingUserName:
          type: string
        roles:
          type: array
          items:
            type: string
          uniqueItems: true
        kurum:
          type: string
          description: Kullanıcı kurumu
          enum:
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - ADLI
          - EMNIYET_SIBER
          - IDB
      required:
      - response
    ChangePasswordRequest:
      type: object
      properties:
        currentPassword:
          type: string
          minLength: 1
        newPassword:
          type: string
          minLength: 1
        confirmPassword:
          type: string
          minLength: 1
      required:
      - confirmPassword
      - currentPassword
      - newPassword
    ChangePasswordResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
      required:
      - response
    GetUserResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        user:
          $ref: "#/components/schemas/MakosUser"
      required:
      - response
    GetUserByIdResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        user:
          $ref: "#/components/schemas/MakosUser"
      required:
      - response
    UsersListResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        users:
          type: array
          items:
            $ref: "#/components/schemas/MakosUser"
      required:
      - response
    TespitTuruDTO:
      type: object
      description: Tespit Turu
      properties:
        tespitTuru:
          type: integer
          format: int64
        aciklama:
          type: string
    TespitTuruListResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        tespitTurleri:
          type: array
          items:
            $ref: "#/components/schemas/TespitTuruDTO"
      required:
      - requestId
      - response
    SucTipiDTO:
      type: object
      description: Suc tipi
      properties:
        sucTipiKodu:
          type: string
        aciklama:
          type: string
        mahkemeKaraTipiKodu:
          type: string
        durum:
          type: string
    SucTipleriResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        sucTipleri:
          type: array
          items:
            $ref: "#/components/schemas/SucTipiDTO"
      required:
      - requestId
      - response
    SorguTipiDTO:
      type: object
      description: Sorgu Tipi
      properties:
        sorguTipi:
          type: integer
          format: int64
        aciklama:
          type: string
    SorguTipiListResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        sorguTipleri:
          type: array
          items:
            $ref: "#/components/schemas/SorguTipiDTO"
      required:
      - requestId
      - response
    MahkemeKodlariResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        mahkemeKodListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKoduDTO"
      required:
      - requestId
      - response
    MahkemeKoduDTO:
      type: object
      description: Mahkeme Bilgi
      properties:
        mahkemeKodu:
          type: string
        mahkemeAdi:
          type: string
    MahkemeKararTipiDTO:
      type: object
      description: Mahkeme Karar Tipi
      properties:
        kararKodu:
          type: integer
          format: int64
        kararTipi:
          type: string
        kararTuru:
          type: string
        sonlandirma:
          type: integer
          format: int64
    MahkemeKararTipleriResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        mahkemeKararTipiListesi:
          type: array
          items:
            $ref: "#/components/schemas/MahkemeKararTipiDTO"
      required:
      - requestId
      - response
    EvrakGelenKurumlarDTO:
      type: object
      description: Evrak gelen kurumlar bilgilerini içerir
      properties:
        id:
          type: integer
          format: int64
          description: Evrak gelen kurum ID
          example: 1
        kurumKod:
          type: string
          description: Kurum kodu
          example: "01"
          maxLength: 10
          minLength: 0
        kurumAdi:
          type: string
          description: Kurum adı
          example: ADALET BAKANLIĞI
          maxLength: 50
          minLength: 0
        kurum:
          type: string
          description: Kurum
          example: ADALET BAKANLIĞI
          maxLength: 64
          minLength: 0
        idx:
          type: integer
          format: int64
          description: Sıra numarası
          example: 1
      required:
      - kurumAdi
      - kurumKod
    EvrakGelenKurumlarResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        kurumlar:
          type: array
          items:
            $ref: "#/components/schemas/EvrakGelenKurumlarDTO"
      required:
      - requestId
      - response
    IllerDTO:
      type: object
      description: İl ve ilçe bilgilerini içerir
      properties:
        ilKod:
          type: string
          description: İl kodu
          example: "0600"
          maxLength: 4
          minLength: 0
        ilAdi:
          type: string
          description: İl adı
          example: ANKARA
          maxLength: 50
          minLength: 0
        ilceAdi:
          type: string
          description: İlçe adı
          example: MERKEZ
          maxLength: 50
          minLength: 0
      required:
      - ilKod
    IllerResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        iller:
          type: array
          items:
            $ref: "#/components/schemas/IllerDTO"
      required:
      - requestId
      - response
    AidiyatResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: "#/components/schemas/MakosApiResponse"
        aidiyatlar:
          type: array
          items:
            $ref: "#/components/schemas/MKTalepAidiyatDTO"
      required:
      - requestId
      - response
    MKTalepAidiyatDTO:
      type: object
      description: Mahkeme Aidiyat Talep bilgilerini içerir
      properties:
        id:
          type: integer
          format: int64
          description: Mahkeme aidiyat talep ID
        mahkemeKararTalepId:
          type: integer
          format: int64
          description: İlişkili mahkeme karar talep ID
        aidiyatKod:
          type: string
          description: Aidiyat kodu
          example: "02"
          maxLength: 25
          minLength: 0
        durumu:
          type: string
          description: Durum
          example: AKTIF
          maxLength: 10
          minLength: 0
      required:
      - aidiyatKod
      - mahkemeKararTalepId
    HealthCheckResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
      required:
      - response
    GetMakosUserAuditLogsByUsernameResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        auditLogs:
          $ref: "#/components/schemas/PageMakosUserAuditLog"
      required:
      - response
    MakosUserAuditLog:
      type: object
      properties:
        id:
          type: integer
          format: int64
        userAuditType:
          type: string
          description: MAKOS kullanıcı denetim türleri
          enum:
          - LOGIN
          - LOGOUT
          - IMPERSONATE_LOGIN
          - CHANGE_PASSWORD
          - GET_USERS_FOR_ADMIN
          - ACTIVATE
          - DEACTIVATE
          - ADD
          - UPDATE
          - DELETE
          - REGISTER
        username:
          type: string
        actingUsername:
          type: string
        userIp:
          type: string
        adminOperatedUsername:
          type: string
        requestTime:
          type: string
          format: date-time
        responseTime:
          type: string
          format: date-time
        responseCode:
          type: integer
          format: int32
    PageMakosUserAuditLog:
      type: object
      properties:
        totalElements:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
        first:
          type: boolean
        last:
          type: boolean
        size:
          type: integer
          format: int32
        content:
          type: array
          items:
            $ref: "#/components/schemas/MakosUserAuditLog"
        number:
          type: integer
          format: int32
        sort:
          $ref: "#/components/schemas/SortObject"
        numberOfElements:
          type: integer
          format: int32
        pageable:
          $ref: "#/components/schemas/PageableObject"
        empty:
          type: boolean
    GetMakosUserAuditLogsByTypeResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        auditLogs:
          $ref: "#/components/schemas/PageMakosUserAuditLog"
      required:
      - response
    GetMakosUserAuditLogListResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        auditLogs:
          $ref: "#/components/schemas/PageMakosUserAuditLog"
      required:
      - response
    GetMakosUserAuditLogResponse:
      type: object
      properties:
        response:
          $ref: "#/components/schemas/ApiResponse"
        auditLog:
          $ref: "#/components/schemas/MakosUserAuditLog"
      required:
      - response
  securitySchemes:
    BasicAuth:
      type: http
      name: Basic Authentication
      in: header
      scheme: basic
