package iym.makos.domain.mktaleprequest.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.domain.mktalep.requestprocessor.processor.IDUzatmaKarariRequestProcessor;
import iym.makos.domain.mktalep.requestprocessor.validator.IMahkemeKararRequestValidator;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static iym.makos.domain.testdata.TestDataBuilder.createTestMakosUser;
import static iym.makos.domain.testdata.TestDataBuilder.createValidIDUzatmaKarariRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDUzatmaKarariRequestProcessor.
 *
 * Tests the processor implementation for ID uzatma kararı requests.
 * Verifies validation, processing, and error handling scenarios.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("IDUzatmaKarariRequestProcessor Unit Tests")
class IDUzatmaKarariRequestProcessorTest extends BaseDomainUnitTest {
    
    @Mock
    private IMahkemeKararRequestValidator<IDUzatmaKarariRequest> mockValidator;
    
    @Mock
    private MahkemeKararDBSaveHandler<IDUzatmaKarariRequest> mockSaver;
    
    @InjectMocks
    private IDUzatmaKarariRequestProcessor processor;
    
    private IDUzatmaKarariRequest testRequest;
    private MakosUserDetails testUser;
    
    @BeforeEach
    void setUp() {
        testRequest = createValidIDUzatmaKarariRequest();
        testUser = createTestMakosUser();
    }
    
    @Test
    @DisplayName("Should process successfully when validation passes and save succeeds")
    void shouldProcessSuccessfully_whenValidationPassesAndSaveSucceeds() throws Exception {
        // Given

        MahkemeKararTalepIdWithEvrakId expected = MahkemeKararTalepIdWithEvrakId.builder()
                .evrakId(12345L)
                .mahkemeKararTalepId(23456L)
                .build();
        ;
        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any()))
                .thenReturn(expected);
        
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseSuccess(response);
        assertThat(response.getEvrakId()).isEqualTo(expected.getEvrakId());
        assertThat(response.getMahkemeKararTalepId()).isEqualTo(expected.getMahkemeKararTalepId());
    }
    
    @Test
    @DisplayName("Should return invalid request response when validation fails")
    void shouldReturnInvalidRequestResponse_whenValidationFails() throws Exception {
        // Given
        ValidationResult invalidResult = new ValidationResult("Validation error");
        when(mockValidator.validate(testRequest)).thenReturn(invalidResult);
        
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).contains("Validation error");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return failed response when save throws exception")
    void shouldReturnFailedResponse_whenSaveThrowsException() throws Exception {
        // Given
        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any()))
                .thenThrow(new RuntimeException("Database error"));
        
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.FAILED);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("Internal Error");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<IDUzatmaKarariRequest> requestType = processor.getRelatedRequestType();
        
        // Then
        assertThat(requestType).isEqualTo(IDUzatmaKarariRequest.class);
    }
    
    @Test
    @DisplayName("Should return correct response type")
    void shouldReturnCorrectResponseType() {
        // When
        Class<IDUzatmaKarariResponse> responseType = processor.getRelatedResponseType();
        
        // Then
        assertThat(responseType).isEqualTo(IDUzatmaKarariResponse.class);
    }
    
    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() {
        // When
        IDUzatmaKarariResponse response = processor.process(null, testUser);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("Request cannot be null");
    }
    
    @Test
    @DisplayName("Should handle null user gracefully")
    void shouldHandleNullUser_gracefully() throws Exception {
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, null);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("User cannot be null");
    }
}
