package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.service.db.GenericDbService;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;


public interface DbDetayMahkemeKararTalepService extends GenericDbService<DetayMahkemeKararTalep, Long> {

    List<DetayMahkemeKararTalep> findByEvrakId(Long evrakId);

    List<DetayMahkemeKararTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    Optional<DetayMahkemeKararTalep> findByMahkemeKararBilgileri(Long evrakId, String mahkemeKararNo, String sorusturmaNo, String mahkemeKodu);

}
