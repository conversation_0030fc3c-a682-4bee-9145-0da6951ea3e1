package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.enums.KararTuru;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mktalep.request.genelevrak.GenelEvrakRequest;
import iym.makos.model.dto.mktalep.request.it.ITKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GenelKararValidator extends MahkemeKararRequestValidatorBase<GenelEvrakRequest> {


    @Autowired
    public GenelKararValidator() {
    }

    @Override
    protected ValidationResult doValidate(GenelEvrakRequest request) {
        try {
            ValidationResult validationResult = new ValidationResult(true);

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.GENEL_EVRAK;
    }

}

