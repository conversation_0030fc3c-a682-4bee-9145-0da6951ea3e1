package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararTalep entity
 */
@Repository
public interface DetayMahkemeKararTalepRepo extends JpaRepository<DetayMahkemeKararTalep, Long> {

    static final String findByMahkemeKararBilgileriSql = """
            SELECT * FROM DMAHKEME_KARAR d
            WHERE
            	d.EVRAK_ID = :evrakId
            	AND d.MAHKEME_KARAR_NO_DETAY = :mahkemeKararNo
            	AND d.SORUSTURMA_NO_DETAY = :sorusturmaNo
            	AND d.MAHKEME_KODU_DETAY = :mahkemeKodu
            """;

    List<DetayMahkemeKararTalep> findByEvrakId(Long evrakId);

    List<DetayMahkemeKararTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    @Query(value = findByMahkemeKararBilgileriSql, nativeQuery = true)
    Optional<DetayMahkemeKararTalep> findByMahkemeKararBilgileri(@Param("evrakId") Long evrakId
            , @Param("mahkemeKararNo") String mahkemeKararNo
            , @Param("sorusturmaNo") String sorusturmaNo
            , @Param("mahkemeKodu") String mahkemeKodu
    );

}
