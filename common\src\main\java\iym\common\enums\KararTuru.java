package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "<PERSON><PERSON><PERSON><PERSON> karar türü", type = "string", allowableValues = {
        "ILETISIMIN_DENETLENMESI_YENI_KARAR",
        "ILETISIMIN_DENETLENMESI_UZATMA_KARARI",
        "ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI",
        "ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME",
        "ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME",
        "ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME",
        "ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME",
        "ILETISIMIN_TESPITI",
        "GENEL_EVRAK",
        "ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME",
        "ILETISIMIN_DENETLENMESI_COKLU_KARAR_TIPLI_KARAR"
})
public enum KararTuru {
    ILETISIMIN_DENETLENMESI_YENI_KARAR(0),
    ILETISIMIN_DENETLENMESI_UZATMA_KARARI(1),
    ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI(2),
    ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME(3),
    ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME(4),
    ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME(5),
    ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME(6),
    ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME(7),
    ILETISIMIN_TESPITI(8),
    GENEL_EVRAK(9),
    ILETISIMIN_DENETLENMESI_COKLU_KARAR_TIPLI_KARAR(10),
    ;


    private final int kararTuru;

    KararTuru(int kararTuru) {
        this.kararTuru = kararTuru;
    }

    @Override
    @JsonValue
    public String toString() {
        return this.name();
    }

    @JsonCreator
    public static KararTuru fromName(String name) {
        for (KararTuru kararTuru : KararTuru.values()) {
            if (kararTuru.name().equals(name)) {
                return kararTuru;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTuru: '" + name + "'");
    }

    //@JsonCreator
    public static KararTuru fromValue(int value) {
        for (KararTuru evrakTuru : KararTuru.values()) {
            if (evrakTuru.kararTuru == value) {
                return evrakTuru;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTuru: '" + value + "'");
    }

    public boolean isIDKararTuru() {
        switch (fromValue(kararTuru)) {
            case ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI,
                 ILETISIMIN_DENETLENMESI_UZATMA_KARARI,
                 ILETISIMIN_DENETLENMESI_YENI_KARAR,
                 ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME,
                 ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME,
                 ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME,
                 ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME,
                 ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME,
                 ILETISIMIN_DENETLENMESI_COKLU_KARAR_TIPLI_KARAR -> {
                return true;
            }
            case GENEL_EVRAK, ILETISIMIN_TESPITI -> {
                return false;
            }
            default -> {
                return false;
            }
        }
    }
}
