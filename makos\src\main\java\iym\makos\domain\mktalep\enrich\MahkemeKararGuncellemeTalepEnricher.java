package iym.makos.domain.mktalep.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararGuncellemeTalep;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararGuncelleTalepService;
import iym.common.util.CommonUtils;
import iym.makos.model.api.MahkemeKararGuncellemeAlanTuru;
import iym.makos.model.dto.view.IDMahkemeBilgiGuncellemeKararDetay;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.MahkemeKararBilgisiGuncellemeInfo;
import iym.makos.model.dto.view.info.MahkemeKararGuncellemeAlanInfo;
import iym.makos.model.dto.view.info.MahkemeKararGuncellemeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class MahkemeKararGuncellemeTalepEnricher implements MKTalepEnricher {

    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararService;
    private final DbMahkemeKararGuncelleTalepService dbMahkemeKararGuncelleService;
    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public MahkemeKararGuncellemeTalepEnricher(DbDetayMahkemeKararTalepService dbDetayMahkemeKararService
            , DbMahkemeKararGuncelleTalepService dbMahkemeKararGuncelleService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbMahkemeKararGuncelleService = dbMahkemeKararGuncelleService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME;
    }


    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {
        try {
            Long mahkemeKararTalepId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararTalepId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararTalepId yok");
            }

            //IDMahkemeBilgiGuncellemeKararDetay

            List<MahkemeKararBilgisiGuncellemeInfo> mahkemeKararBilgisiGuncellemeList = new ArrayList<>();

            //Detay MahkemeKarar Listesini Al.
            List<DetayMahkemeKararTalep> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKarar -> {


                DetayMahkemeKararInfo mahkemeKararDetayInfo = getMahkemeKararDetayInfo(detayMahkemeKarar);

                MahkemeKararGuncellemeDTO mahkemeKararGuncellemeDTO = new MahkemeKararGuncellemeDTO();

                List<MahkemeKararGuncellemeAlanInfo> guncellemeAlanListesi = new ArrayList<>();

                Optional<MahkemeKararGuncellemeTalep> mahkemeKararGuncellemeOpt = dbMahkemeKararGuncelleService.findByDetayMahkemeKararTalepId(detayMahkemeKarar.getId());
                if (mahkemeKararGuncellemeOpt.isPresent()) {
                    MahkemeKararGuncellemeTalep mahkemeKararGuncelleme = mahkemeKararGuncellemeOpt.get();

                    if (CommonUtils.safeString(mahkemeKararGuncelleme.getUpdateColumnNames()).contains(MahkemeKararGuncellemeAlanTuru.MAHKEME_KODU.getValue())) {
                        MahkemeKararGuncellemeAlanInfo mahkemeKararGuncellemeAlanInfo = MahkemeKararGuncellemeAlanInfo.builder()
                                .mahkemeKararGuncellemeAlanTuru(MahkemeKararGuncellemeAlanTuru.MAHKEME_KODU)
                                .yeniDegeri(mahkemeKararGuncelleme.getMahkemeKararNo())
                                .build();
                        guncellemeAlanListesi.add(mahkemeKararGuncellemeAlanInfo);
                    } else if (CommonUtils.safeString(mahkemeKararGuncelleme.getUpdateColumnNames()).contains(MahkemeKararGuncellemeAlanTuru.SORUSTURMA_NO.getValue())) {
                        MahkemeKararGuncellemeAlanInfo mahkemeKararGuncellemeAlanInfo = MahkemeKararGuncellemeAlanInfo.builder()
                                .mahkemeKararGuncellemeAlanTuru(MahkemeKararGuncellemeAlanTuru.SORUSTURMA_NO)
                                .yeniDegeri(mahkemeKararGuncelleme.getSorusturmaNo())
                                .build();
                        guncellemeAlanListesi.add(mahkemeKararGuncellemeAlanInfo);
                    } else if (CommonUtils.safeString(mahkemeKararGuncelleme.getUpdateColumnNames()).contains(MahkemeKararGuncellemeAlanTuru.MAHKEME_KARAR_NO.getValue())) {
                        MahkemeKararGuncellemeAlanInfo mahkemeKararGuncellemeAlanInfo = MahkemeKararGuncellemeAlanInfo.builder()
                                .mahkemeKararGuncellemeAlanTuru(MahkemeKararGuncellemeAlanTuru.MAHKEME_KARAR_NO)
                                .yeniDegeri(mahkemeKararGuncelleme.getMahkemeKodu())
                                .build();
                        guncellemeAlanListesi.add(mahkemeKararGuncellemeAlanInfo);
                    }

                    mahkemeKararGuncellemeDTO.setDetayMahkemeKararId(detayMahkemeKarar.getId());
                    mahkemeKararGuncellemeDTO.setGuncellemeAlanListesi(guncellemeAlanListesi);
                    mahkemeKararGuncellemeDTO.setId(mahkemeKararGuncelleme.getId());

                    MahkemeKararBilgisiGuncellemeInfo mahkemeKararBilgisiGuncellemeInfo = MahkemeKararBilgisiGuncellemeInfo.builder()
                            .detayMahkemeKararInfo(mahkemeKararDetayInfo)
                            .mahkemeKararGuncellemeDTO(mahkemeKararGuncellemeDTO)
                            .build();
                    mahkemeKararBilgisiGuncellemeList.add(mahkemeKararBilgisiGuncellemeInfo);

                }
            });

            IDMahkemeBilgiGuncellemeKararDetay mahkemeBilgiGuncellemeKararDetay = IDMahkemeBilgiGuncellemeKararDetay.builder()
                    .guncellemeListesi(mahkemeKararBilgisiGuncellemeList)
                    .build();

            mahkemeKararIslem.setIDMahkemeBilgiGuncellemeKararDetay(mahkemeBilgiGuncellemeKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("MahkemeKararGuncellemeTalepEnricher failed. mahkemeKararTalepId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }

    }



    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararTalep detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

}



