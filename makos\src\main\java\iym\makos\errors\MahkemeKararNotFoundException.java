package iym.makos.errors;

import iym.makos.model.MakosResponseCode;

public class MahkemeKararNotFoundException extends MakosResponseException{
    public MahkemeKararNotFoundException(String message) {
        super(message);
    }

    public MahkemeKararNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public MahkemeKararNotFoundException(String errorCode, Object... args) {
        super(errorCode, args);
    }

    public MahkemeKararNotFoundException(MakosResponseCode makosResponseCode, String errorCode, Object... args) {
        super(makosResponseCode, errorCode, args);
    }
}