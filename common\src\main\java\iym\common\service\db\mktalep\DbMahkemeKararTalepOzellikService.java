package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.talep.MahkemeKararTalepOzellik;
import iym.common.service.db.GenericDbService;

import java.util.Optional;

/**
 * Service interface for MahkemeKararTalepOzellik entity
 */
public interface DbMahkemeKararTalepOzellikService extends GenericDbService<MahkemeKararTalepOzellik, Long> {

    Optional<MahkemeKararTalepOzellik> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

}
