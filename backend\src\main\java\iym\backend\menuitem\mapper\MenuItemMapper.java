package iym.backend.menuitem.mapper;

import org.mapstruct.*;
import iym.backend.menuitem.dto.MenuItemDto;
import iym.backend.menuitem.entity.MenuItem;
import iym.backend.shared.mapper.BaseMapper;

@Mapper(componentModel = "spring",
        mappingInheritanceStrategy = MappingInheritanceStrategy.AUTO_INHERIT_ALL_FROM_CONFIG)
public interface MenuItemMapper extends BaseMapper<MenuItem, MenuItemDto> {
    @Mapping(target = "yetkiIds", expression = "java(menuItem.getMenuItemYetkiler() != null ? menuItem.getMenuItemYetkiler().stream().map(y -> y.getYetki().getId()).toList() : java.util.Collections.emptyList())")
    @Mapping(target = "yetkiAdlari", expression = "java(menuItem.getMenuItemYetkiler() != null ? menuItem.getMenuItemYetkiler().stream().map(y -> y.getYetki().getAd()).toList() : java.util.Collections.emptyList())")
    @Mapping(target = "parentId", expression = "java(menuItem.getParent() != null ? menuItem.getParent().getId() : null)")
    @Mapping(target = "items", expression = "java(menuItem.getChildren() != null ? menuItem.getChildren().stream().map(this::toDto).toList() : java.util.Collections.emptyList())")
    MenuItemDto toDto(MenuItem menuItem);

    @InheritInverseConfiguration
    @Mapping(target = "children", ignore = true) // recursive mapping handled manually if needed
    @Mapping(target = "parent", ignore = true)   // parent entity set edilmesi business logic ile yapılabilir
    MenuItem toEntity(MenuItemDto dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "parent", ignore = true)
    void updateEntityFromDto(MenuItemDto dto, @MappingTarget MenuItem entity);
}
