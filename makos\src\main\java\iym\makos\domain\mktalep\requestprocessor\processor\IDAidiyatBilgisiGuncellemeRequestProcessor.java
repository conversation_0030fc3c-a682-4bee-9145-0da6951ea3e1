package iym.makos.domain.mktalep.requestprocessor.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.IDAidiyatBilgisiGuncellemeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IDAidiyatBilgisiGuncellemeRequestProcessor extends MakosRequestProcessorBase<IDAidiyatBilgisiGuncellemeRequest, IDAidiyatBilgisiGuncellemeResponse> {

    @Override
    public IDAidiyatBilgisiGuncellemeResponse process(IDAidiyatBilgisiGuncellemeRequest request, MakosUserDetails islemYapanKullanici) {

        MakosApiResponse preCheck = preCheck(request, islemYapanKullanici);
        log.error("IDAidiyatBilgisiGuncelleme process failed: reason:{}", preCheck.getResponseMessage());
        if (preCheck.getResponseCode() != MakosResponseCode.SUCCESS){
            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage(preCheck.getResponseMessage())
                            .build())
                    .build();
        }

        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {

                return IDAidiyatBilgisiGuncellemeResponse.builder()
                        .requestId(request.getId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            MkTalepDbSaveRequest<IDAidiyatBilgisiGuncellemeRequest> saveRequest = MkTalepDbSaveRequest.<IDAidiyatBilgisiGuncellemeRequest>builder()
                    .kararRequest(request)
                    .kullaniciId(islemYapanKullanici.getUserId())
                    .build();
            MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = requestSaver.kaydet(saveRequest);

            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepIdWithEvrakId.getEvrakId())
                    .mahkemeKararTalepId(mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId())
                    .build();

        } catch (MakosResponseException ex) {
            // Let MakosResponseException propagate with its detailed message
            log.error("IDAidiyatBilgisiGuncelleme process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        } catch (Exception ex) {
            // Null-safe logging - null checks already done above
            log.error("IDAidiyatBilgisiGuncelleme process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        }
    }

}
