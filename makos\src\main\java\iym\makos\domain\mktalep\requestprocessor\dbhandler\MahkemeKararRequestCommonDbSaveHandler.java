package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.EvrakKurum;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.util.CommonUtils;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.utils.UtilService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class MahkemeKararRequestCommonDbSaveHandler {

    private final UtilService utilService;
    private final KararRequestMapper kararRequestMapper;
    private final DbEvrakKayitService dbEvrakKayitService;

    @Autowired
    public MahkemeKararRequestCommonDbSaveHandler(UtilService utilService, KararRequestMapper kararRequestMapper, DbEvrakKayitService dbEvrakKayitService) {
        this.utilService = utilService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbEvrakKayitService = dbEvrakKayitService;
    }

    public Long saveEvrakBilgileri(MkTalepDbSaveRequest<?> request) {

        MkTalepRequest kararRequest = request.getKararRequest();

        EvrakDetay evrakDetay = kararRequest.getEvrakDetay();
        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        if (CommonUtils.isNullOrEmpty(evrakGelenKurumKodu)) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "evrakKurumKodu boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        MahkemeKararTip kararTipi = kararRequest.getMahkemeKararBilgisi().getMahkemeKararTipi();
        String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

        // Evrak kaydet
        EvrakKayit savedEvrak = doSaveEvrakBilgileri(evrakDetay, evrakTipi, request.getKullaniciId(), kararRequest.getKararTuru());
        if (savedEvrak == null || savedEvrak.getId() == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        return savedEvrak.getId();
    }

    private EvrakKayit doSaveEvrakBilgileri(EvrakDetay evrakDetay, String evrakTipi, Long kaydedenKullaniciId, KararTuru kararTuru) {

        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        if (CommonUtils.isNullOrEmpty(evrakGelenKurumKodu)) {
            String errorStr = CommonUtils.getFormattedString(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "evrakKurumKodu boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
        //Evrak sira numarasi
        String evrakSiraNo = utilService.getEvrakSiraNumarasi(evrakDetay.getEvrakKurumKodu(), evrakDetay.getEvrakTuru().name());
        if (CommonUtils.isNullOrEmpty(evrakSiraNo)) {
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_SIRANO_ALINAMADI);
        }
        //Save Evrak
        EvrakKayit evrakKayit = kararRequestMapper.toEvrakKayit(evrakDetay, evrakSiraNo, evrakTipi, LocalDateTime.now(), kaydedenKullaniciId);

        return dbEvrakKayitService.save(evrakKayit);
    }
}
