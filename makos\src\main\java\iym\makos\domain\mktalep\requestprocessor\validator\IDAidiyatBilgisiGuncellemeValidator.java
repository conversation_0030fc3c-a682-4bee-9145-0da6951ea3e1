package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.GuncellemeTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mktalep.request.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDAidiyatBilgisiGuncellemeRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class IDAidiyatBilgisiGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDAidiyatBilgisiGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService;

    @Autowired
    public IDAidiyatBilgisiGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService,
                                               DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararAidiyatService = dbMahkemeKararAidiyatService;

    }

    @Override
    protected ValidationResult doValidate(IDAidiyatBilgisiGuncellemeRequest request) {

        try {
            ValidationResult validationResult = new ValidationResult(true);

            MahkemeKararTip mahkemeKararTip = request.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            IDAidiyatBilgisiGuncellemeRequestDetay guncellemeRequestDetay = request.getAidiyatBilgisiGuncellemeRequestDetay();
            if (guncellemeRequestDetay == null) {
                validationResult.addFailedReason("AidiyatBilgisiGuncellemeRequestDetay boş olamaz");
                return validationResult;
            }

            for (AidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay : CommonUtils.safeList(guncellemeRequestDetay.getAidiyatGuncellemeKararDetayListesi())) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = aidiyatGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkili mahkeme karar boş olamaz.");
                } else {
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu(), iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {

                        for (AidiyatGuncellemeDetay aidiyatGuncellemeDetay : CommonUtils.safeList(aidiyatGuncellemeKararDetay.getAidiyatGuncellemeDetayListesi())) {
                            String aidiyatKodu = aidiyatGuncellemeDetay.getAidiyatKodu();

                            String aidiyatUyari = CommonUtils.aidiyatEklemeUyarisi(aidiyatKodu, mahkemeKararTip, evrakKurum);
                            if(!CommonUtils.isNullOrEmpty(aidiyatUyari)){
                                validationResult.addFailedReason(aidiyatUyari);
                            }

                            Optional<MahkemeAidiyat> aidiyatOpt = dbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(iliskiliMahkemeKararOpt.get().getId(), aidiyatKodu);
                            if (aidiyatGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.EKLE) {
                                if (aidiyatOpt.isPresent()) {
                                    validationResult.addFailedReason(aidiyatKodu + " aidiyatı mahkeme karar bilgisinde bulunduğu için eklenemez. ");
                                }
                            } else if (aidiyatGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.CIKAR) {
                                if (aidiyatOpt.isEmpty()) {
                                    validationResult.addFailedReason(aidiyatKodu + " aidiyatı mahkeme karar bilgisinde bulunamadığı için çıkarılamaz.");
                                }
                            }
                        }
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }
}

