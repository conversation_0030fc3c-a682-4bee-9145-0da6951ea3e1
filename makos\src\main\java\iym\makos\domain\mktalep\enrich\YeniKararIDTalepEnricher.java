package iym.makos.domain.mktalep.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.enums.SureTip;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.model.entity.iym.talep.MahkemeAidiyatTalep;
import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeSuclarTalepService;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDYeniKararDetay;
import iym.makos.model.dto.view.info.*;
import iym.makos.model.dto.view.mapper.HedeflerIslemEnricherMapper;
import iym.makos.model.dto.view.mapper.KararAidiyatIslemEnricherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class YeniKararIDTalepEnricher implements MKTalepEnricher {

    private final DbHedeflerTalepService dbHedeflerIslemService;
    private final HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper;
    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatService;
    private final DbMahkemeAidiyatTalepService dbMahkemeAidiyatService;
    private final KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper;
    private final DbMahkemeSuclarTalepService dbMahkemeSuclarService;
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public YeniKararIDTalepEnricher(
            DbHedeflerTalepService dbHedeflerIslemService
            , HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper
            , DbHedeflerAidiyatTalepService dbHedeflerAidiyatService
            , DbMahkemeAidiyatTalepService dbMahkemeAidiyatService
            , KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper
            , DbMahkemeSuclarTalepService dbMahkemeSuclarService
            , DbSucTipiService dbSucTipiService

    ) {
        this.dbHedeflerIslemService = dbHedeflerIslemService;
        this.hedeflerIslemEnricherMapper = hedeflerIslemEnricherMapper;
        this.dbHedeflerAidiyatService = dbHedeflerAidiyatService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;
        this.iDKararAidiyatIslemEnricherMapper = iDKararAidiyatIslemEnricherMapper;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {
        try {
            Long mahkemeKararTalepId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararTalepId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararTalepId yok");
            }

            //enrich only IDYeniKararDetay

            //Kararin Hedefler Listesi
            List<HedeflerTalep> hedeflerList = dbHedeflerIslemService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            List<YeniIDKarariHedefInfo> iDYeniHedefInfoList = enrichHedefler(hedeflerList);

            //Kararin Aidiyat Listsi
            List<MahkemeAidiyatTalep> aidiyatListesi = dbMahkemeAidiyatService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            List<IDKarariAidiyatInfo> iDKarariAidiyatInfoList = enrichAidiyatListesi(aidiyatListesi);

            //Kararin Suc Tipleri
            List<MahkemeSuclarTalep> sucLitesi = dbMahkemeSuclarService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            List<IDKarariSucTipiInfo> sucTipleriDTOList = enrichSucTipleri(sucLitesi, mahkemeKararTalepId);

            //IDMahkemeKarariInfo'in enrich edilecek nesnesi : MkIslemIDYeniKararDTO
            IDYeniKararDetay yeniKararDetay = IDYeniKararDetay.builder()
                    .hedefListesi(iDYeniHedefInfoList)
                    .hedefListesi(iDYeniHedefInfoList)
                    .mahkemeAidiyatlari(iDKarariAidiyatInfoList)
                    .sucTipleri(sucTipleriDTOList)
                    .build();
            mahkemeKararIslem.setIDYeniKararDetay(yeniKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("YeniKararIDEnricher failed. mahkemeKararTalepId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }

    private List<IDKarariSucTipiInfo> enrichSucTipleri(List<MahkemeSuclarTalep> kararSucTipleri, Long mahkemeKararId) {
        List<IDKarariSucTipiInfo> result = new ArrayList<>();
        List<SucTipi> sucTipiListesi = dbSucTipiService.getByMahkemeTalepId(mahkemeKararId);
        CommonUtils.safeList(kararSucTipleri).forEach(mahkemeKararSucTipi -> {

            SucTipi sucTipi = sucTipiListesi.stream()
                    .filter(s -> s.getSucTipiKodu().equals(mahkemeKararSucTipi.getSucTipKodu()))
                    .findFirst()
                    .orElse(null);

            SucTipiInfo sucTipiInfo = SucTipiInfo.builder()
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipAdi(sucTipi != null ? sucTipi.getAciklama() : "")
                    .build();

            IDKarariSucTipiInfo iDKarariSucTipiInfo = IDKarariSucTipiInfo.builder()
                    .id(mahkemeKararSucTipi.getId())
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipi(sucTipiInfo)
                    .mahkemeKararId(mahkemeKararId)
                    .build();

            result.add(iDKarariSucTipiInfo);

        });

        return result;
    }

    private List<YeniIDKarariHedefInfo> enrichHedefler(List<HedeflerTalep> list) {
        List<YeniIDKarariHedefInfo> result = new ArrayList<>();
        list.forEach(hedeflerTalep -> {

            IDHedefInfo iDHedefInfo = hedeflerIslemEnricherMapper.toIDHedefInfo(hedeflerTalep);

            // TODO neden kullanilmiyor?
            YeniIDKarariHedefInfo yeniIDKarariHedefInfo = hedeflerIslemEnricherMapper.toYeniIDKarariHedefInfo(hedeflerTalep);


            //Hedeflerin aidiyatlarini set et.
            List<HedeflerAidiyatTalep> hedeflerAidiyatList = dbHedeflerAidiyatService.findByHedeflerTalepId(hedeflerTalep.getId());
            List<IDHedefAidiyatInfo> aidiyatList = hedeflerIslemEnricherMapper.toIDHedefAidiyatInfoList(hedeflerAidiyatList);

            YeniIDKarariHedefInfo iDYeniHedefDetayInfo = YeniIDKarariHedefInfo.builder()
                    .hedefBilgileri(iDHedefInfo)
                    .baslamaTarihi(hedeflerTalep.getBaslamaTarihi())
                    .canakNo(hedeflerTalep.getCanakNo())
                    .sure(hedeflerTalep.getSuresi())
                    .sureTip(SureTip.fromValue(hedeflerTalep.getSureTipi()))
                    .hedefAidiyatListesi(aidiyatList)
                    .build();

            result.add(iDYeniHedefDetayInfo);
        });

        return result;
    }

    private List<IDKarariAidiyatInfo> enrichAidiyatListesi(List<MahkemeAidiyatTalep> aidiyatListesi) {
        return iDKararAidiyatIslemEnricherMapper.toIDKarariAidiyatList(aidiyatListesi);
    }

}



