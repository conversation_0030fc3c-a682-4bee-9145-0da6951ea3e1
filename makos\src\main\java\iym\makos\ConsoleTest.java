package iym.makos;

import iym.common.enums.*;
import iym.common.model.api.Hedef;
import iym.common.model.api.HedefWithAdSoyad;
import iym.common.model.api.Response;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.mktalep.requestprocessor.service.MahkemeKararProcessService;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararBilgisi;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariResponse;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDUzatmaKarariRequestDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDYeniKararRequestDetay;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.service.mk.MahkemeKararService;
import iym.makos.service.mktalep.MahkemeKararTalepService;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Jacksonized
@NoArgsConstructor
@SuperBuilder
@MakosRequestValid
@Slf4j

//@SpringBootApplication
public class ConsoleTest implements CommandLineRunner {

    @Autowired
    MahkemeKararService mahkemeKararService;

    @Autowired
    MahkemeKararTalepService mahkemeKararTalepService;

    @Autowired
    private MahkemeKararProcessService mahkemeKararProcessService;

    public static void main(String[] args) {
        /**
         new SpringApplicationBuilder(ConsoleTest.class)
         .web(WebApplicationType.NONE)
         .run(args);
         */
        //ApplicationContext context = SpringApplication.run(ConsoleTest.class, args);
        //System.out.println("Makos Application is main!");
    }

    @Override
    public void run(String... args) {

        try {

            //Kaydedilen ve elde edilen nesnelerin butun field'lar bazinda tutuarli olup olmadigini kontrol etmek icin

            UserDetailsImpl user = UserDetailsImpl.builder()
                    .userId(1L)
                    .build();

            //IDYeniKararRequest yeniIDRequest = getYeniIDRequest();

            IDUzatmaKarariRequest uzatmaIDRequest = getUzatmaIDRequest();


            IDUzatmaKarariResponse response = mahkemeKararProcessService.process(uzatmaIDRequest, IDUzatmaKarariResponse.class, MakosUserDetails.fromUserDetails(user));

            Response<MahkemeKarariInfo> mahkemeKarariInfoRes1 = mahkemeKararTalepService.getMahkemeKararTalepDetails(10l, true);


            System.out.println("Makos Application is running!");
        } catch (Exception e) {
            // Handle any exceptions that occur during the application run
            log.error("Error occurred while running the application: ", e);
        }
    }


    private IDYeniKararRequest getYeniIDRequest() {

        IDYeniKararRequest request = IDYeniKararRequest.builder()
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR)
                .fileName("dosya1.pdf")
                .mahkemeKararBilgisi(
                        MahkemeKararBilgisi.builder()
                                .mahkemeKararTipi(MahkemeKararTip.ONLEYICI_HAKIM_KARARI)
                                .mahkemeKararDetay(
                                        MahkemeKararDetay.builder()
                                                .mahkemeKararNo("2025/123456")
                                                .mahkemeKararNo("2024/234556")
                                                .mahkemeKodu("06000101")
                                                .mahkemeIlIlceKodu("0601")
                                                .aciklama("Ankara test evraki")
                                                .build()
                                )
                                .build()
                )
                .evrakDetay(
                        EvrakDetay.builder()
                                .evrakTuru(EvrakTuru.ILETISIMIN_DENETLENMESI)
                                .evrakKurumKodu("03")
                                .evrakNo("IDB2025-0002")
                                .evrakTarihi(LocalDateTime.of(2025, 01, 01, 13, 30))
                                .geldigiIlIlceKodu("0601")
                                .acilmi(true)
                                .aciklama("Test Evrak Açıklaması2")
                                .build()
                )
                .yeniKararRequestDetay(
                        IDYeniKararRequestDetay.builder()
                                .hedefDetayListesi(List.of(
                                        IDHedefDetay.builder()
                                                //.baslamaTarihi(new LocalDateTime())

                                                .hedefNoAdSoyad(
                                                        HedefWithAdSoyad.builder()
                                                                .tcKimlikNo("59389091111")
                                                                .hedefAd("Ali")
                                                                .hedefSoyad("KARA")
                                                                .hedef(
                                                                        Hedef.builder()
                                                                                .hedefNo("55511121111")
                                                                                .hedefTip(HedefTip.GSM)
                                                                                .build()
                                                                )
                                                                .build()
                                                )
                                                .baslamaTarihi(LocalDateTime.of(2025, 9, 01, 13, 30))
                                                .sureTip(SureTip.AY)
                                                .sure(3)
                                                .canakNo("")
                                                .hedefAidiyatKodlari(List.of("HA1", "HA2"))
                                                .build(),

                                        IDHedefDetay.builder()
                                                .hedefNoAdSoyad(
                                                        HedefWithAdSoyad.builder()
                                                                .tcKimlikNo("59389092222")
                                                                .hedefAd("Veli")
                                                                .hedefSoyad("AK")
                                                                .hedef(
                                                                        Hedef.builder()
                                                                                .hedefNo("55511141111")
                                                                                .hedefTip(HedefTip.GSM)
                                                                                .build()
                                                                )
                                                                .build()
                                                )
                                                .baslamaTarihi(LocalDateTime.of(2025, 9, 01, 13, 30))
                                                .sureTip(SureTip.AY)
                                                .sure(3)
                                                .canakNo("")
                                                .hedefAidiyatKodlari(List.of("HA4"))
                                                .build()

                                ))
                                .mahkemeSucTipiKodlari(List.of("18195", "18225", "18265"))
                                .mahkemeAidiyatKodlari(List.of("Jİ_AID1", "Jİ_AID2", "Jİ_AID3", "Jİ_AID4"))
                                .build())
                .build();

        return request;
    }


    private IDUzatmaKarariRequest getUzatmaIDRequest() {

        IDUzatmaKarariRequest request = IDUzatmaKarariRequest.builder()
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                .fileName("dosya1.pdf")
                .mahkemeKararBilgisi(
                        MahkemeKararBilgisi.builder()
                                .mahkemeKararTipi(MahkemeKararTip.ONLEYICI_HAKIM_KARARI)
                                .mahkemeKararDetay(
                                        MahkemeKararDetay.builder()
                                                .mahkemeKararNo("2025/223456")
                                                .mahkemeKararNo("2024/334556")
                                                .mahkemeKodu("06000101")
                                                .mahkemeIlIlceKodu("0601")
                                                .aciklama("Ankara uzatma test evraki")
                                                .build()
                                )
                                .build()
                )
                .evrakDetay(
                        EvrakDetay.builder()
                                .evrakTuru(EvrakTuru.ILETISIMIN_DENETLENMESI)
                                .evrakKurumKodu("03")
                                .evrakNo("IDB2025-0003")
                                .evrakTarihi(LocalDateTime.of(2025, 02, 01, 13, 30))
                                .geldigiIlIlceKodu("0601")
                                .acilmi(true)
                                .aciklama("Uzatma Test Evrak Açıklaması2")
                                .build()
                )
                .uzatmaKarariRequestDetay(IDUzatmaKarariRequestDetay.builder()
                        .hedefDetayListesi(List.of(
                                IDHedefDetay.builder()
                                        //.baslamaTarihi(new LocalDateTime())

                                        .hedefNoAdSoyad(
                                                HedefWithAdSoyad.builder()
                                                        .tcKimlikNo("59389091111")
                                                        .hedefAd("Ali")
                                                        .hedefSoyad("KARA")
                                                        .hedef(
                                                                Hedef.builder()
                                                                        .hedefNo("55511121111")
                                                                        .hedefTip(HedefTip.GSM)
                                                                        .build()
                                                        )
                                                        .build()
                                        )
                                        .baslamaTarihi(LocalDateTime.of(2025, 9, 01, 13, 30))
                                        .sureTip(SureTip.AY)
                                        .sure(3)
                                        .canakNo("")
                                        .hedefAidiyatKodlari(List.of("HA1", "HA2"))
                                        .uzatmaSayisi(1)
                                        .ilgiliMahkemeKararDetayi(
                                                MahkemeKararDetay.builder()
                                                        .mahkemeKararNo("MK-2023-001")
                                                        .sorusturmaNo("2025/123")
                                                        .mahkemeKodu("06000101")
                                                        .mahkemeIlIlceKodu("0600")
                                                        .build()
                                        )
                                        .build(),

                                IDHedefDetay.builder()
                                        .hedefNoAdSoyad(
                                                HedefWithAdSoyad.builder()
                                                        .tcKimlikNo("59389092222")
                                                        .hedefAd("Ahmet")
                                                        .hedefSoyad("Yılmaz")
                                                        .hedef(
                                                                Hedef.builder()
                                                                        .hedefNo("55511131111")
                                                                        .hedefTip(HedefTip.GSM)
                                                                        .build()
                                                        )
                                                        .build()
                                        )
                                        .baslamaTarihi(LocalDateTime.of(2025, 9, 1, 13, 30))
                                        .sureTip(SureTip.AY)
                                        .sure(3)
                                        .canakNo("")
                                        .uzatmaSayisi(1)
                                        .ilgiliMahkemeKararDetayi(
                                                MahkemeKararDetay.builder()
                                                        .mahkemeKararNo("MK-2023-001")
                                                        .sorusturmaNo("2025/123")
                                                        .mahkemeKodu("06000101")
                                                        .mahkemeIlIlceKodu("0600")
                                                        .build()
                                        )
                                        .hedefAidiyatKodlari(List.of("HA4"))
                                        .build())
                        )
                        .mahkemeSucTipiKodlari(List.of("18195", "18225", "18265"))
                        .mahkemeAidiyatKodlari(List.of("Jİ_AID1", "Jİ_AID2", "Jİ_AID3", "Jİ_AID4"))
                        .build())

                .build();

        return request;
    }


}