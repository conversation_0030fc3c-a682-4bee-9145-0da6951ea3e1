/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ITYeniKararDetay } from './iTYeniKararDetay';
import { IDAidiyatGuncellemeKararDetay } from './iDAidiyatGuncellemeKararDetay';
import { IDUzatmaKararDetay } from './iDUzatmaKararDetay';
import { EvrakDetayInfo } from './evrakDetayInfo';
import { IDMahkemeBilgiGuncellemeKararDetay } from './iDMahkemeBilgiGuncellemeKararDetay';
import { MahkemeKararBilgisiInfo } from './mahkemeKararBilgisiInfo';
import { IDSucTipiGuncellemeKararDetay } from './iDSucTipiGuncellemeKararDetay';
import { IDSonlandirmaKararDetay } from './iDSonlandirmaKararDetay';
import { IDYeniKararDetay } from './iDYeniKararDetay';
import { IDHedefGuncellemeKararDetay } from './iDHedefGuncellemeKararDetay';


export interface MahkemeKarariInfo { 
    requestId?: string;
    mahkemeKararId?: number;
    kararTuru?: MahkemeKarariInfo.KararTuruEnum;
    evrakDetay?: EvrakDetayInfo;
    mahkemeKararBilgisi?: MahkemeKararBilgisiInfo;
    dosyaAdi?: string;
    durumu?: string;
    idsonlandirmaKararDetay?: IDSonlandirmaKararDetay;
    idaidiyatGuncellemeKararDetay?: IDAidiyatGuncellemeKararDetay;
    idhedefGuncellemeKararDetay?: IDHedefGuncellemeKararDetay;
    idsucTipiGuncellemeKararDetay?: IDSucTipiGuncellemeKararDetay;
    iduzatmaKararDetay?: IDUzatmaKararDetay;
    idyeniKararDetay?: IDYeniKararDetay;
    idmahkemeBilgiGuncellemeKararDetay?: IDMahkemeBilgiGuncellemeKararDetay;
    ityeniKararDetay?: ITYeniKararDetay;
}
export namespace MahkemeKarariInfo {
    export const KararTuruEnum = {
        IletisiminDenetlenmesiYeniKarar: 'ILETISIMIN_DENETLENMESI_YENI_KARAR',
        IletisiminDenetlenmesiUzatmaKarari: 'ILETISIMIN_DENETLENMESI_UZATMA_KARARI',
        IletisiminDenetlenmesiSonlandirmaKarari: 'ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI',
        IletisiminDenetlenmesiAidiyatGuncelleme: 'ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME',
        IletisiminDenetlenmesiHedefGuncelleme: 'ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME',
        IletisiminDenetlenmesiMahkemekararGuncelleme: 'ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME',
        IletisiminDenetlenmesiCanakGuncelleme: 'ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME',
        IletisiminTespiti: 'ILETISIMIN_TESPITI',
        GenelEvrak: 'GENEL_EVRAK',
        IletisiminDenetlenmesiSuctipiGuncelleme: 'ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME'
    } as const;
    export type KararTuruEnum = typeof KararTuruEnum[keyof typeof KararTuruEnum];
}


