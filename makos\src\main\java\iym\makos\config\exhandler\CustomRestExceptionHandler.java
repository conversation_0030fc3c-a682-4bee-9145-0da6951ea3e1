package iym.makos.config.exhandler;

import iym.common.enums.ResponseCode;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.util.EnvironmentUtil;
import iym.common.util.ProblemDetailUtils;
import iym.common.model.api.ProblemDetailBuilder;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.MakosResponseCode;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.ConversionFailedException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ProblemDetail;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.*;

@ControllerAdvice
@Slf4j
public class CustomRestExceptionHandler extends ResponseEntityExceptionHandler {

    private final EnvironmentUtil environmentUtil;

    public CustomRestExceptionHandler(EnvironmentUtil environmentUtil) {
        this.environmentUtil = environmentUtil;
    }



    /**
     * Get safe error message for production
     */
    private String getSafeErrorMessage(String originalMessage, String fallbackMessage) {
        if (environmentUtil.isDevelopmentEnvironment()) {
            return originalMessage;
        } else {
            // In production, return generic message to avoid exposing internal details
            return fallbackMessage;
        }
    }

    /**
     * Get current authenticated user for logging purposes
     */
    private String getCurrentUser() {
        try {
            return org.springframework.security.core.context.SecurityContextHolder
                    .getContext()
                    .getAuthentication()
                    .getName();
        } catch (Exception e) {
            return "anonymous";
        }
    }

    @ExceptionHandler({ConstraintViolationException.class})
    public ResponseEntity<ProblemDetail> handleConstraintViolationException(ConstraintViolationException ex, WebRequest request) {

        List<String> errors = new ArrayList<>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            String fieldPath = violation.getPropertyPath().toString();
            String message = violation.getMessage();
            Object invalidValue = violation.getInvalidValue();

            String errorDetail = String.format("%s: %s (invalid value: %s)",
                    fieldPath, message, invalidValue);
            errors.add(errorDetail);
        }

        String path = ((ServletWebRequest) request).getRequest().getRequestURI();
        String method = ((ServletWebRequest) request).getRequest().getMethod();
        String userAgent = ((ServletWebRequest) request).getRequest().getHeader("User-Agent");
        String errorMessage = String.join("; ", errors);

        ProblemDetail problemDetail = ProblemDetailUtils.forConstraintViolation(errorMessage);

        // Enhanced logging for constraint violations
        log.error("Validation Failed - ConstraintViolation: uri:{}, method:{}, user:{}, userAgent:{}, errors:{}",
                path, method, getCurrentUser(), userAgent, errors, ex);
        return new ResponseEntity<>(problemDetail, HttpStatus.valueOf(problemDetail.getStatus()));
    }

    @ExceptionHandler({ResponseStatusException.class})
    public ResponseEntity<ProblemDetail> handleResponseStatusException(ResponseStatusException rse) {
        String safeMessage = getSafeErrorMessage(rse.getReason(), "Request failed");
        ProblemDetail problemDetail = ProblemDetailUtils.forGenericException(safeMessage, !environmentUtil.isDevelopmentEnvironment());

        // Always log full details for debugging
        log.error("ResponseStatusException: status:{}, reason:{}", rse.getStatusCode(), rse.getReason(), rse);
        return new ResponseEntity<>(problemDetail, rse.getStatusCode());
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String error = ex.getMessage();
        Throwable rootCause = ex.getRootCause();

        if (rootCause != null) {
            if (rootCause instanceof IllegalArgumentException) {
                error = rootCause.getMessage();
            } else if (rootCause instanceof com.fasterxml.jackson.databind.exc.InvalidFormatException) {
                com.fasterxml.jackson.databind.exc.InvalidFormatException jacksonEx =
                    (com.fasterxml.jackson.databind.exc.InvalidFormatException) rootCause;
                error = String.format("Invalid format for field '%s': expected type %s, got value '%s'",
                        jacksonEx.getPath().get(jacksonEx.getPath().size() - 1).getFieldName(),
                        jacksonEx.getTargetType().getSimpleName(),
                        jacksonEx.getValue());
            } else if (rootCause instanceof com.fasterxml.jackson.databind.exc.MismatchedInputException) {
                error = "JSON structure error: " + rootCause.getMessage();
            }
        }

        String uri = ((ServletWebRequest) request).getRequest().getRequestURI();
        String method = ((ServletWebRequest) request).getRequest().getMethod();
        String userAgent = ((ServletWebRequest) request).getRequest().getHeader("User-Agent");

        ProblemDetail problemDetail = ProblemDetailUtils.forHttpMessageNotReadable("Invalid request format: " + error);

        // Enhanced logging for JSON parsing errors
        log.error("Validation Failed - HttpMessageNotReadable: uri:{}, method:{}, user:{}, userAgent:{}, error:{}",
                uri, method, getCurrentUser(), userAgent, error, ex);
        return new ResponseEntity<>(problemDetail, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {

        List<String> errors = new ArrayList<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            if (error instanceof FieldError) {
                String fieldName = ((FieldError) error).getField();
                String objectName = ((FieldError) error).getObjectName();
                errors.add(objectName + "." + fieldName + ": " + error.getDefaultMessage());
            } else {
                errors.add(error.getDefaultMessage());
            }
        });

        String errorMessage = String.join("; ", errors);
        ProblemDetail problemDetail = ProblemDetailUtils.forConstraintViolation(errorMessage);

        // Enhanced logging for validation failures
        String uri = ((ServletWebRequest) request).getRequest().getRequestURI();
        String method = ((ServletWebRequest) request).getRequest().getMethod();
        String userAgent = ((ServletWebRequest) request).getRequest().getHeader("User-Agent");

        log.error("Validation Failed - MethodArgumentNotValid: uri:{}, method:{}, user:{}, userAgent:{}, errors:{}",
                uri, method, getCurrentUser(), userAgent, errors, ex);
        return new ResponseEntity<>(problemDetail, status);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(MissingServletRequestParameterException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        ProblemDetail problemDetail = ProblemDetailUtils.forMissingParameter(ex.getParameterName());

        // Always log full details for debugging
        log.error("MissingServletRequestParameter. uri:{}, parameter:{}", ((ServletWebRequest) request).getRequest().getRequestURI(), ex.getParameterName(), ex);
        return new ResponseEntity<>(problemDetail, headers, status);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ProblemDetail> handleIllegalArgumentException(IllegalArgumentException ex) {
        String error = ex.getMessage();
        ProblemDetail problemDetail = ProblemDetailUtils.forIllegalArgument(error, !environmentUtil.isDevelopmentEnvironment());

        // Always log full details for debugging
        log.error("IllegalArgumentException. error:{}", error, ex);
        return new ResponseEntity<>(problemDetail, HttpStatus.valueOf(problemDetail.getStatus()));
    }

    @ExceptionHandler(ConversionFailedException.class)
    public ResponseEntity<ProblemDetail> handleConversionFailed(RuntimeException e) {
        ProblemDetail problemDetail = ProblemDetailUtils.forConversionFailed(e.getMessage(), !environmentUtil.isDevelopmentEnvironment());

        // Always log full details for debugging
        log.error("ConversionFailed: {}", e.getMessage(), e);
        return new ResponseEntity<>(problemDetail, HttpStatus.valueOf(problemDetail.getStatus()));
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ProblemDetail> handleAccessDeniedException(AccessDeniedException ex, WebRequest request) {
        String path = ((ServletWebRequest) request).getRequest().getRequestURI();
        String message = "Access Denied: You do not have the required role to access this resource";

        // Access denied messages are safe to show in both environments
        ProblemDetail problemDetail = ProblemDetailBuilder.authorizationError(message);

        // Always log full details for debugging
        log.error("Access Denied. uri:{}, user:{}, error:{}", path, getCurrentUser(), ex.getMessage(), ex);
        return new ResponseEntity<>(problemDetail, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ProblemDetail> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex, WebRequest request) {
        String error = ex.getName() + " should be of type " + Objects.requireNonNull(ex.getRequiredType()).getName();
        ProblemDetail problemDetail = ProblemDetailUtils.forIllegalArgument(error, !environmentUtil.isDevelopmentEnvironment());

        // Always log full details for debugging
        log.error("MethodArgumentTypeMismatch. uri:{}, parameter:{}, expectedType:{}, actualValue:{}",
                ((ServletWebRequest) request).getRequest().getRequestURI(),
                ex.getName(),
                ex.getRequiredType().getName(),
                ex.getValue(), ex);
        return new ResponseEntity<>(problemDetail, HttpStatus.valueOf(problemDetail.getStatus()));
    }

    @ExceptionHandler({MakosResponseException.class})
    public ResponseEntity<ProblemDetail> handleMakosResponseException(MakosResponseException e) {
        // For MakosResponseException, we want to expose the formatted message
        String errorMessage = e.getFormattedMessage();
        HttpStatus httpStatus = e.getMakosResponseCode() != null ?
            MakosResponseCode.toHttpStatus(e.getMakosResponseCode()) : HttpStatus.BAD_REQUEST;

        ProblemDetail problemDetail = ProblemDetailUtils.forMakosApiException(errorMessage, httpStatus, !environmentUtil.isDevelopmentEnvironment());

        // Always log full details for debugging
        log.error("MakosResponseException: {}", errorMessage, e);
        return new ResponseEntity<>(problemDetail, httpStatus);
    }

    @ExceptionHandler({Exception.class})
    public ResponseEntity<ProblemDetail> handleAll(Exception e) {
        // For generic exceptions, never expose internal details in production
        String safeMessage = getSafeErrorMessage(e.getMessage(), "Internal Server Error");
        ProblemDetail problemDetail = ProblemDetailUtils.forGenericException(safeMessage, !environmentUtil.isDevelopmentEnvironment());

        // Always log full details for debugging
        log.error("Unhandled Exception: {}", e.getMessage(), e);
        return new ResponseEntity<>(problemDetail, HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
