package iym.makos.model.dto.mktalep.request.id.detay;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.SucTipiGuncellemeDetay;
import iym.makos.model.api.SucTipiGuncellemeKararDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class IDSucTipiGuncellemeRequestDetay implements IDRequestDetay {

    @NotNull
    @Valid
    @Size(min = 1)
    @Schema(description = "Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri")
    private List<SucTipiGuncellemeKararDetay> sucTipiGuncellemeKararDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDSucTipiGuncellemeRequestDetay is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (sucTipiGuncellemeKararDetayListesi == null || sucTipiGuncellemeKararDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Güncellemeye konu olan en az bir detay girilmelidir!");
                return validationResult;
            }

            for (SucTipiGuncellemeKararDetay sucTipiGuncellemeKararDetay : sucTipiGuncellemeKararDetayListesi) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = sucTipiGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                }
                List<SucTipiGuncellemeDetay> guncellemeListesi = sucTipiGuncellemeKararDetay.getSucTipiGuncellemeDetayListesi();
                if (guncellemeListesi == null || guncellemeListesi.isEmpty()) {
                    validationResult.addFailedReason("Güncellenecek suçtipi listesi boş olamaz.!");
                }
            }

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

}

