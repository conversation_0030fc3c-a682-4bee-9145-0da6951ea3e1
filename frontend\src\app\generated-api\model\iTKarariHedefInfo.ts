/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface ITKarariHedefInfo { 
    hedefNo?: string;
    hedefTip?: ITKarariHedefInfo.HedefTipEnum;
    hedefAdi?: string;
    hedefSoyadi?: string;
}
export namespace ITKarariHedefInfo {
    export const HedefTipEnum = {
        Gsm: 'GSM',
        Sabit: 'SABIT',
        Uydu: 'UYDU',
        YurtDisi: 'YURT_DISI',
        UmthMsisdn: 'UMTH_MSISDN',
        UmthUsername: 'UMTH_USERNAME',
        UmthIp: 'UMTH_IP',
        UmthPincode: 'UMTH_PINCODE',
        Eposta: 'EPOSTA',
        IpTakip: 'IP_TAKIP',
        UrlWebAdresiTakip: 'URL_WEB_ADRESI_TAKIP',
        AdslAboneTakip: 'ADSL_ABONE_TAKIP',
        Gprs: 'GPRS',
        IpEngelleme: 'IP_ENGELLEME',
        DomainEngelleme: 'DOMAIN_ENGELLEME',
        Imei: 'IMEI',
        Imsi: 'IMSI',
        GprsImsi: 'GPRS_IMSI',
        XdslMsisdn: 'XDSL_MSISDN',
        XdslTemosno: 'XDSL_TEMOSNO',
        XdslUsername: 'XDSL_USERNAME',
        XdslIp: 'XDSL_IP',
        GprsGsm: 'GPRS_GSM',
        GprsImei: 'GPRS_IMEI',
        GprsYurtDisi: 'GPRS_YURT_DISI',
        Trunk: 'TRUNK',
        GsmYerTespiti: 'GSM_YER_TESPITI',
        GsmYerTespitiSonlandirma: 'GSM_YER_TESPITI_SONLANDIRMA',
        YurtdisiYerTespiti: 'YURTDISI_YER_TESPITI',
        YurtdisiYerTespitiSonlandirma: 'YURTDISI_YER_TESPITI_SONLANDIRMA'
    } as const;
    export type HedefTipEnum = typeof HedefTipEnum[keyof typeof HedefTipEnum];
}


