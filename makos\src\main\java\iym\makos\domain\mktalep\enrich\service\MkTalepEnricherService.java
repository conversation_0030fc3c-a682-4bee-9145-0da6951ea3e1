package iym.makos.domain.mktalep.enrich.service;

import iym.common.enums.KararTuru;
import iym.common.model.api.Response;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MkTalepEnricherService {

    private final MkTalepEnricherFactory mkTalepEnricherFactory;

    @Autowired
    public MkTalepEnricherService(MkTalepEnricherFactory mkIslemEnricherFactory) {
        this.mkTalepEnricherFactory = mkIslemEnricherFactory;
    }

    public Response<String> enrich(MahkemeKarariInfo iDMahkemeKarariInfo) {
        return mkTalepEnricherFactory.enrich(iDMahkemeKarariInfo);
    }

    public Response<String>  enrich(MahkemeKarariInfo iDMahkemeKarariInfo, KararTuru kararTuru) {
        return mkTalepEnricherFactory.enrich(iDMahkemeKarariInfo, kararTuru);
    }

}
