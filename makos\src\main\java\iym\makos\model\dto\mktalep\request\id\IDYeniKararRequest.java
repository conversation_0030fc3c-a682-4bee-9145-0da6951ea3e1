package iym.makos.model.dto.mktalep.request.id;

import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDYeniKararRequestDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDYeniKararRequest extends MkTalepRequest {

    @NotNull
    @Valid
    private IDYeniKararRequestDetay yeniKararRequestDetay;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDYeniKararRequest is valid");

        try {

            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR.name() + " olmalıdır");
                return validationResult;
            }

            if (yeniKararRequestDetay == null) {
                validationResult.addFailedReason("IDYeniKararRequestDetay boş olamaz");
                return validationResult;
            }

            MahkemeKararTip mahkemeKararTipi = mahkemeKararBilgisi.getMahkemeKararTipi();
            boolean yeniMahkemeKararTipinde = CommonUtils.yeniMahkemeKararTipi(mahkemeKararTipi);
            if (!yeniMahkemeKararTipinde) {
                validationResult.addFailedReason("Mahkeme karar tipi, yeni karar için uygun değildir!");
            }

            ValidationResult yeniKararRequestDetayValid = yeniKararRequestDetay.isValid();
            if (!yeniKararRequestDetayValid.isValid()){
                yeniKararRequestDetayValid.getReasons().forEach(validationResult::addFailedReason);
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }
}

