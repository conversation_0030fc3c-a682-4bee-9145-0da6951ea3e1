package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.model.entity.iym.talep.HtsHedeflerTalep;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.service.db.mktalep.DbHtsHedeflerTalepService;
import iym.common.service.db.mktalep.DbHtsMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.ITHedefDetay;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.it.ITKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
public class ITKararDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<ITKararRequest> {

    private final KararRequestMapper kararRequestMapper;
    private final DbHtsHedeflerTalepService dbHtsHedeflerTalepService;
    private final DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;

    @Autowired
    public ITKararDBSaveHandler(DbHtsHedeflerTalepService dbHtsHedeflerTalepService, KararRequestMapper kararRequestMapper, DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService) {
        this.dbHtsHedeflerTalepService = dbHtsHedeflerTalepService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbHtsMahkemeKararTalepService = dbHtsMahkemeKararTalepService;
    }

    @Override
    public Long saveMahkemeKararTalep(Long evrakId, MkTalepDbSaveRequest<ITKararRequest> request){

        validateRequest(request);

        ITKararRequest kararRequest = request.getKararRequest();

        LocalDateTime saveDateTime = LocalDateTime.now();
        HtsMahkemeKararTalep talep = kararRequestMapper.toHTSMahkemeKararTalep(
                kararRequest.getMahkemeKararBilgisi(),
                evrakId,
                request.getKullaniciId(),
                saveDateTime
        );

        HtsMahkemeKararTalep savedMahkemeKararTalep = dbHtsMahkemeKararTalepService.save(talep);
        if (savedMahkemeKararTalep == null || savedMahkemeKararTalep.getId() == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.MKTALEP_KAYIT_HATASI);
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        return savedMahkemeKararTalep.getId();
    }

    @Override
    public void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<ITKararRequest> request) {

        validateRequest(request);

        ITKararRequest kararRequest = request.getKararRequest();

        LocalDateTime saveDateTime = LocalDateTime.now();
        Long htsMahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();


        for (ITHedefDetay iTHedefDetay : kararRequest.getHedefDetayListesi()) {

            HtsHedeflerTalep htsHedeflerTalep = HtsHedeflerTalep.builder()
                    .hedefNo(iTHedefDetay.getHedef().getHedefNo())
                    .baslangicTarihi(iTHedefDetay.getBaslamaTarihi())
                    .bitisTarihi(iTHedefDetay.getBitisTarihi())
                    .mahkemeKararId(htsMahkemeKararTalepId)
                    .durumu(null)
                    .build();

            HtsHedeflerTalep savedHtsHedeflerTalep = dbHtsHedeflerTalepService.save(htsHedeflerTalep);
            if(savedHtsHedeflerTalep == null){
                String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.HTSTALEP_HEDEF_KAYIT_HATASI, iTHedefDetay.getHedef().getHedefNo());
                log.error(errorStr);
                throw new MakosResponseException(errorStr);
            }
        }
    }
}

