package iym.makos.model.dto.mktalep.request.it;

import iym.common.enums.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.ITHedefDetay;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@SuperBuilder
@Jacksonized
@ToString
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class ITKararRequest extends MkTalepRequest {

    @NotNull
    @Size(min = 1)
    @Valid
    private List<ITHedefDetay> hedefDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if ITKararRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_TESPITI) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_TESPITI.name() + " olmalıdır");
                return validationResult;
            }

            //TODO
            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_TESPITI;
    }
}

