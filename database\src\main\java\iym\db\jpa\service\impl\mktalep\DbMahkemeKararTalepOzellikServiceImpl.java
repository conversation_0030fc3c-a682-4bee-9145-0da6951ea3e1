package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.MahkemeKararTalepOzellik;
import iym.common.service.db.mktalep.DbMahkemeKararTalepOzellikService;
import iym.db.jpa.dao.mktalep.MahkemeKararTalepOzellikRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalepOzellik entity
 */
@Service
public class DbMahkemeKararTalepOzellikServiceImpl extends GenericDbServiceImpl<MahkemeKararTalepOzellik, Long> implements DbMahkemeKararTalepOzellikService {

    private final MahkemeKararTalepOzellikRepo mahkemeKararTalepOzellikRepo;

    @Autowired
    public DbMahkemeKararTalepOzellikServiceImpl(MahkemeKararTalepOzellikRepo repository) {
        super(repository);
        this.mahkemeKararTalepOzellikRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKararTalepOzellik> findByMahkemeKararTalepId(Long mahkemeKararTalepId){
        return mahkemeKararTalepOzellikRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }



}
