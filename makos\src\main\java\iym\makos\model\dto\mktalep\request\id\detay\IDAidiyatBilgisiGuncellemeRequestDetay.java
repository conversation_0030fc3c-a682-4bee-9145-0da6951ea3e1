package iym.makos.model.dto.mktalep.request.id.detay;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class IDAidiyatBilgisiGuncellemeRequestDetay implements IDRequestDetay {

    @NotNull
    @Valid
    @Size(min = 1)
    private List<AidiyatGuncellemeKararDetay> aidiyatGuncellemeKararDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDAidiyatBilgisiGuncellemeRequestDetay is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (aidiyatGuncellemeKararDetayListesi == null || aidiyatGuncellemeKararDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Güncellemeye konu olan en az bir detay girilmelidir!");
                return validationResult;
            }

            for (AidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay : aidiyatGuncellemeKararDetayListesi) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = aidiyatGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                }
                List<AidiyatGuncellemeDetay> guncellemeListesi = aidiyatGuncellemeKararDetay.getAidiyatGuncellemeDetayListesi();
                if (guncellemeListesi == null || guncellemeListesi.isEmpty()) {
                    validationResult.addFailedReason("Güncellenecek aidiyat listesi boş olamaz.!");
                }
            }

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

}

