package iym.makos.model.dto.mktalep.request.id.detay;

import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.IDHedefDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
/*
Herbir hedefin icinde ayri bir detay tutmaktansa, bir detay karara ait
birden fazla herdef gonderilebilirdi. Boyce daha statil bir yapiya kavusurur
* */

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class IDSonlandirmaKarariRequestDetay implements IDRequestDetay {

    @NotNull
    @Size(min = 1)
    @Valid
    private List<IDHedefDetay> hedefDetayListesi;

    private List<String> mahkemeAidiyatKodlari;

    private List<String> mahkemeSucTipiKodlari;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDSonlandirmaKarariRequestDetay is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (hedefDetayListesi == null || hedefDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Sonlandırma kararında hedef detay listesi boş olamaz.");
                return validationResult;
            }

            for (IDHedefDetay IDHedefDetay : hedefDetayListesi) {
                if (IDHedefDetay.getIlgiliMahkemeKararDetayi() == null) {
                    validationResult.addFailedReason("Sonlandirma Kararinda ilgili mahkeme karari bos olamaz!");
                }

                if (!CommonUtils.isNullOrEmpty(IDHedefDetay.getCanakNo())) {
                    validationResult.addFailedReason("Sonlandirma kararında CANAK numarası girilemez.");
                }

                if (IDHedefDetay.getUzatmaSayisi() != null) {
                    validationResult.addFailedReason("Sonlandirma Kararinda uzatma sayisi dolu olamaz!");
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

}

