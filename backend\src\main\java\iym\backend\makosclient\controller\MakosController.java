package iym.backend.makosclient.controller;

import iym.backend.makosclient.service.MakosApiService;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.makos.api.client.gen.model.*;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * MAKOS Controller
 * MAKOS API işlemlerini expose eden REST controller
 * Spring best practices ile multipart file upload handling
 */
@RestController
@RequestMapping("/api/makos")
@RequiredArgsConstructor
@Slf4j
@Validated
public class MakosController {

    private static final String FILE_PART = "mahkemeKararDosyasi";
    private static final String REQUEST_PART = "mahkemeKararDetay";

    private final MakosApiService makosApiService;

    /**
     * Health check endpoint
     *
     * @return Health check response
     */
    @GetMapping("/health")
    public ResponseEntity<HealthCheckResponse> healthCheck() {
        try {
            HealthCheckResponse response = makosApiService.healthCheck();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Health check failed", e);
            var apiResponse = new ModelApiResponse();
            apiResponse.setResponseMessage(e.getMessage());
            apiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.FAILED);
            HealthCheckResponse failedResponse = new HealthCheckResponse();
            failedResponse.setResponse(apiResponse);
            return ResponseEntity.internalServerError().body(failedResponse);
        }
    }

    /**
     * Health check endpoint with basic authorization
     *
     * @return Health check response
     */
    @GetMapping("/healthCheckAuthorized")
    public ResponseEntity<HealthCheckResponse> healthCheckAuthorized() {
        try {
            HealthCheckResponse response = makosApiService.healthCheckAuthorized();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Health check with basic authorization failed", e);
            HealthCheckResponse failedResponse = new HealthCheckResponse();
            ModelApiResponse failedApiResponse = new ModelApiResponse();
            failedApiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.FAILED);
            failedApiResponse.setResponseMessage(e.getMessage());
            failedResponse.setResponse(failedApiResponse);
            return ResponseEntity.internalServerError().body(failedResponse);
        }
    }

    /**
     * Mahkeme bilgisi güncelleme endpoint
     * File upload ile mahkeme bilgisi güncelleme işlemi
     * Spring best practice: @RequestPart ile otomatik binding ve validasyon
     *
     * @param request Mahkeme bilgisi güncelleme isteği
     * @return Güncelleme sonucu
     */
    @PostMapping(value = "/mahkeme-bilgisi-guncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDMahkemeKararGuncellemeResponse> mahkemeBilgisiGuncelle(
            @Valid @RequestPart(value = REQUEST_PART) IDMahkemeKararGuncellemeRequest request,
            @RequestPart(value = FILE_PART) MultipartFile file) {

        try {
            log.info("Mahkeme bilgisi güncelleme isteği alındı - ID: {}, Mahkeme Kodu: {}",
                    request.getId(),
                    request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu());

            // MultipartFile'ı File'a çevir
            File tempFile = convertMultipartFileToFile(file);

            // MAKOS API'yi çağır
            IDMahkemeKararGuncellemeResponse response = makosApiService.mahkemeBilgisiGuncelle(tempFile, request);

            // Temp dosyayı sil
            tempFile.delete();

            log.info("Mahkeme bilgisi güncelleme başarılı - ID: {}", request.getId());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Mahkeme bilgisi güncelleme hatası - ID: {}",
                    request.getId(), e);
            IDMahkemeKararGuncellemeResponse errorResponse = new IDMahkemeKararGuncellemeResponse();
            MakosApiResponse apiResponse = new MakosApiResponse();
            apiResponse.setResponseCode(MakosApiResponse.ResponseCodeEnum.FAILED);
            apiResponse.setResponseMessage("Güncelleme hatası: " + e.getMessage());
            errorResponse.setResponse(apiResponse);
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Yeni ID karar ekleme endpoint
     * File upload ile ID tipi yeni karar ekleme
     * Spring best practice: @RequestPart ile otomatik binding ve validasyon
     *
     * @param file               Mahkeme karar dosyası
     * @param idYeniKararRequest Karar bilgileri
     * @return Karar sonucu
     */
    @PostMapping(value = "/yeni-karar-id", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IDYeniKararResponse>> yeniKararID(
            @RequestPart(value = FILE_PART) MultipartFile file,
            @Valid @RequestPart(value = REQUEST_PART) IDYeniKararRequest idYeniKararRequest) throws IOException {

        log.info("Yeni ID karar isteği alındı - Mahkeme Kodu: {}, Dosya Adı: {}, Dosya Boyutu: {} bytes",
                idYeniKararRequest.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu(),
                file.getOriginalFilename(),
                file.getSize());

        // MultipartFile'ı File'a çevir
        File tempFile = convertMultipartFileToFile(file);

        // MAKOS API'yi çağır
        Response<IDYeniKararResponse> serviceResponse = makosApiService.kararGonderID(tempFile, idYeniKararRequest);
        // Temp dosyayı sil
        tempFile.delete();

        log.info("Yeni ID karar eklenemedi - BTK Evrak ID: {}", serviceResponse.getResultDetails());

        if (!serviceResponse.isSuccess()) {

            return ResponseEntity.badRequest().body(serviceResponse);
        }

        log.info("Yeni ID karar başarıyla eklendi - BTK Evrak ID: {}",
                serviceResponse.getResult().getEvrakId());
        return ResponseEntity.ok(serviceResponse);
    }

    /**
     * Yeni Karar IT ekleme endpoint
     * File upload ile yeni IT kararı ekleme işlemi
     * Spring best practice: @RequestPart ile otomatik binding ve validasyon
     *
     * @param file    Mahkeme karar dosyası
     * @param request ITKararRequest objesi
     * @return Ekleme sonucu
     */
    @PostMapping(value = "/yeni-karar-it", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ITKararResponse> yeniKararIT(
            @RequestPart(value = FILE_PART) MultipartFile file,
            @Valid @RequestPart(value = REQUEST_PART) ITKararRequest request) {

        try {
            log.info("Yeni IT karar isteği alındı - Mahkeme Kodu: {}", request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu());

            // MultipartFile'ı File'a çevir
            File tempFile = convertMultipartFileToFile(file);

            // MAKOS API'yi çağır
            ITKararResponse response = makosApiService.yenikararIT(tempFile, request);

            // Temp dosyayı sil
            tempFile.delete();

            log.info("Yeni IT karar başarıyla eklendi");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Yeni Karar IT ekleme hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(null);
        }
    }

    /**
     * İl ilçe kodları listesi endpoint
     *
     * @return İl listesi
     */
    @GetMapping(value = "/iller", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IllerResponse>> iller() {
        Response<IllerResponse> response = makosApiService.iller();
        return ResponseEntity.ok(response);
    }

    /**
     * Kurum kodları listesi endpoint
     *
     * @return Kurum listesi
     */
    @GetMapping(value = "/kurumlar", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<EvrakGelenKurumlarResponse>> kurumlar() {
        Response<EvrakGelenKurumlarResponse> kurumlar = makosApiService.kurumlar();
        return ResponseEntity.ok(kurumlar);
    }

    /**
     * Mahkeme kodları listesi endpoint
     *
     * @return Mahkeme kodları listesi
     */
    @GetMapping(value = "/mahkeme-kodlari", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<MahkemeKodlariResponse>> mahkemeKodlari() {
        Response<MahkemeKodlariResponse> response = makosApiService.mahkemeKodlari();
        return ResponseEntity.ok(response);
    }

    /**
     * Suc tipleri listesi endpoint
     *
     * @return Suc tipleri listesi
     */
    @GetMapping(value = "/suc-tipleri", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<SucTipleriResponse>> sucTipleri() {
        Response<SucTipleriResponse> response = makosApiService.sucTipleri();
        return ResponseEntity.ok(response);
    }

    /**
     * Sorgu tipleri listesi endpoint
     *
     * @return Sorgu tipleri listesi
     */
    @GetMapping(value = "/sorgu-tipleri", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<SorguTipiListResponse>> sorguTipleri() {
        Response<SorguTipiListResponse> response = makosApiService.sorguTipleri();
        return ResponseEntity.ok(response);
    }

    /**
     * Mahkeme karar tipleri listesi endpoint
     *
     * @return Mahkeme karar tipleri listesi
     */
    @GetMapping(value = "/mahkeme-karar-tipleri", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<MahkemeKararTipleriResponse>> mahkemeKararTipleri() {
        Response<MahkemeKararTipleriResponse> response = makosApiService.mahkemeKararTipleri();
        return ResponseEntity.ok(response);
    }

    /**
     * Tespit turleri listesi endpoint
     *
     * @return Tespit turleri listesi
     */
    @GetMapping(value = "/tespit-turleri", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<TespitTuruListResponse>> tespitTurleri() {
        Response<TespitTuruListResponse> response = makosApiService.tespitTurleri();
        return ResponseEntity.ok(response);
    }

    /**
     * Mahkeme karar talep sorgulama endpoint
     * Tüm mahkeme karar talep kayıtlarını sorgular
     *
     * @param request Sorgulama parametreleri
     * @return Sorgulama sonuçları
     */
    @PostMapping(value = "/mahkeme-karar-talep-sorgu", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IDMahkemeKararTalepSorgulamaResponse>> mahkemeKararTalepSorgu(
            @Valid @RequestBody IDMahkemeKararTalepSorgulamaRequest request) {

        try {
            log.info("Mahkeme karar talep sorgulama isteği alındı - ID: {}", request.getId());

            // MAKOS API'yi çağır
            IDMahkemeKararTalepSorgulamaResponse response = makosApiService.mahkemeKararTalepSorgu(request);

            log.info("Mahkeme karar talep sorgulama başarılı - ID: {}", request.getId());
            return ResponseEntity.ok(new Response<>(response));

        } catch (Exception e) {
            log.error("Mahkeme karar talep sorgulama hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(new Response<>(ResultCode.FAILED, "Sorgulama hatası: " + e.getMessage()));
        }
    }

    /**
     * Mahkeme karar talep bilgisi sorgulama endpoint
     *
     * @param request Mahkeme karar talep bilgisi sorgulama isteği
     * @return Mahkeme karar talep bilgisi
     */
    @PostMapping(value = "/mahkeme-karar-talep-bilgisi", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<MahkemeKararTalepQueryResponse>> mahkemeKararTalepBilgisi(
            @Valid @RequestBody MahkemeKararTalepBilgisiRequest request) {

        try {
            log.info("Mahkeme karar talep bilgisi sorgulama isteği alındı");

            // MAKOS API'yi çağır
            MahkemeKararTalepQueryResponse response = makosApiService.mahkemeKararTalepBilgisi(request);

            log.info("Mahkeme karar talep bilgisi sorgulama başarılı");
            return ResponseEntity.ok(new Response<>(response));

        } catch (Exception e) {
            log.error("Mahkeme karar talep bilgisi sorgulama hatası", e);
            return ResponseEntity.internalServerError().body(new Response<>(ResultCode.FAILED, "Sorgulama hatası: " + e.getMessage()));
        }
    }

    /**
     * Sonlandırma kararı ID endpoint
     * File upload ile sonlandırma kararı gönderme işlemi
     *
     * @param file    Mahkeme karar dosyası
     * @param request Sonlandırma kararı isteği
     * @return Sonlandırma kararı sonucu
     */
    @PostMapping(value = "/sonlandirma-karari-id", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IDSonlandirmaKarariResponse>> sonlandirmaKarariID(
            @RequestPart(value = FILE_PART) MultipartFile file,
            @Valid @RequestPart(value = REQUEST_PART) IDSonlandirmaKarariRequest request) throws IOException {

        log.info("Sonlandırma kararı ID isteği alındı - ID: {}, Dosya Adı: {}, Dosya Boyutu: {} bytes",
                request.getId(),
                file.getOriginalFilename(),
                file.getSize());

        // MultipartFile'ı File'a çevir
        File tempFile = convertMultipartFileToFile(file);

        try {
            // MAKOS API'yi çağır
            IDSonlandirmaKarariResponse response = makosApiService.sonlandirmaKarariID(tempFile, request);

            log.info("Sonlandırma kararı ID başarıyla gönderildi - ID: {}", request.getId());
            return ResponseEntity.ok(new Response<>(response));

        } catch (Exception e) {
            log.error("Sonlandırma kararı ID hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(new Response<>(ResultCode.FAILED, "Sonlandırma kararı hatası: " + e.getMessage()));
        } finally {
            // Temp dosyayı sil
            tempFile.delete();
        }
    }

    /**
     * Suç tipi güncelleme endpoint
     * File upload ile suç tipi güncelleme işlemi
     *
     * @param file    Mahkeme karar dosyası
     * @param request Suç tipi güncelleme isteği
     * @return Suç tipi güncelleme sonucu
     */
    @PostMapping(value = "/suc-tipi-guncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IDSucTipiGuncellemeResponse>> sucTipiGuncelle(
            @RequestPart(value = FILE_PART) MultipartFile file,
            @Valid @RequestPart(value = REQUEST_PART) IDSucTipiGuncellemeRequest request) throws IOException {

        log.info("Suç tipi güncelleme isteği alındı - ID: {}, Dosya Adı: {}, Dosya Boyutu: {} bytes",
                request.getId(),
                file.getOriginalFilename(),
                file.getSize());

        // MultipartFile'ı File'a çevir
        File tempFile = convertMultipartFileToFile(file);

        try {
            // MAKOS API'yi çağır
            IDSucTipiGuncellemeResponse response = makosApiService.sucTipiGuncelle(tempFile, request);

            log.info("Suç tipi güncelleme başarılı - ID: {}", request.getId());
            return ResponseEntity.ok(new Response<>(response));

        } catch (Exception e) {
            log.error("Suç tipi güncelleme hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(new Response<>(ResultCode.FAILED, "Suç tipi güncelleme hatası: " + e.getMessage()));
        } finally {
            // Temp dosyayı sil
            tempFile.delete();
        }
    }

    /**
     * Uzatma kararı ID endpoint
     * File upload ile uzatma kararı gönderme işlemi
     *
     * @param file    Mahkeme karar dosyası
     * @param request Uzatma kararı isteği
     * @return Uzatma kararı sonucu
     */
    @PostMapping(value = "/uzatma-karari-id", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IDUzatmaKarariResponse>> uzatmaKarariID(
            @RequestPart(value = FILE_PART) MultipartFile file,
            @Valid @RequestPart(value = REQUEST_PART) IDUzatmaKarariRequest request) throws IOException {

        log.info("Uzatma kararı ID isteği alındı - ID: {}, Dosya Adı: {}, Dosya Boyutu: {} bytes",
                request.getId(),
                file.getOriginalFilename(),
                file.getSize());

        // MultipartFile'ı File'a çevir
        File tempFile = convertMultipartFileToFile(file);

        try {
            // MAKOS API'yi çağır
            IDUzatmaKarariResponse response = makosApiService.uzatmaKarariID(tempFile, request);

            log.info("Uzatma kararı ID başarıyla gönderildi - ID: {}", request.getId());
            return ResponseEntity.ok(new Response<>(response));

        } catch (Exception e) {
            log.error("Uzatma kararı ID hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(new Response<>(ResultCode.FAILED, "Uzatma kararı hatası: " + e.getMessage()));
        } finally {
            // Temp dosyayı sil
            tempFile.delete();
        }
    }

    /**
     * Aidiyat bilgisi güncelleme endpoint
     * File upload ile aidiyat bilgisi güncelleme işlemi
     *
     * @param file    Mahkeme karar dosyası
     * @param request Aidiyat bilgisi güncelleme isteği
     * @return Aidiyat bilgisi güncelleme sonucu
     */
    @PostMapping(value = "/aidiyat-bilgisi-guncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IDAidiyatBilgisiGuncellemeResponse>> aidiyatBilgisiGuncelle(
            @RequestPart(value = FILE_PART) MultipartFile file,
            @Valid @RequestPart(value = REQUEST_PART) IDAidiyatBilgisiGuncellemeRequest request) throws IOException {

        log.info("Aidiyat bilgisi güncelleme isteği alındı - ID: {}, Dosya Adı: {}, Dosya Boyutu: {} bytes",
                request.getId(),
                file.getOriginalFilename(),
                file.getSize());

        // MultipartFile'ı File'a çevir
        File tempFile = convertMultipartFileToFile(file);

        try {
            // MAKOS API'yi çağır
            IDAidiyatBilgisiGuncellemeResponse response = makosApiService.aidiyatBilgisiGuncelle(tempFile, request);

            log.info("Aidiyat bilgisi güncelleme başarılı - ID: {}", request.getId());
            return ResponseEntity.ok(new Response<>(response));

        } catch (Exception e) {
            log.error("Aidiyat bilgisi güncelleme hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(new Response<>(ResultCode.FAILED, "Aidiyat bilgisi güncelleme hatası: " + e.getMessage()));
        } finally {
            // Temp dosyayı sil
            tempFile.delete();
        }
    }

    /**
     * Hedef bilgisi güncelleme endpoint
     * File upload ile hedef bilgisi güncelleme işlemi
     *
     * @param file    Mahkeme karar dosyası
     * @param request Hedef bilgisi güncelleme isteği
     * @return Hedef bilgisi güncelleme sonucu
     */
    @PostMapping(value = "/hedef-bilgisi-guncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response<IDHedefGuncellemeResponse>> hedefBilgisiGuncelle(
            @RequestPart(value = FILE_PART) MultipartFile file,
            @Valid @RequestPart(value = REQUEST_PART) IDHedefGuncellemeRequest request) throws IOException {

        log.info("Hedef bilgisi güncelleme isteği alındı - ID: {}, Dosya Adı: {}, Dosya Boyutu: {} bytes",
                request.getId(),
                file.getOriginalFilename(),
                file.getSize());

        // MultipartFile'ı File'a çevir
        File tempFile = convertMultipartFileToFile(file);

        try {
            // MAKOS API'yi çağır
            IDHedefGuncellemeResponse response = makosApiService.hedefBilgisiGuncelle(tempFile, request);

            log.info("Hedef bilgisi güncelleme başarılı - ID: {}", request.getId());
            return ResponseEntity.ok(new Response<>(response));

        } catch (Exception e) {
            log.error("Hedef bilgisi güncelleme hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(new Response<>(ResultCode.FAILED, "Hedef bilgisi güncelleme hatası: " + e.getMessage()));
        } finally {
            // Temp dosyayı sil
            tempFile.delete();
        }
    }

    /**
     * MultipartFile'ı File'a çevirir
     *
     * @param multipartFile MultipartFile
     * @return File
     * @throws IOException IO hatası
     */
    private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        // Temp dosya oluştur
        Path tempFile = Files.createTempFile("file_upload_", UUID.randomUUID() + "_" + multipartFile.getOriginalFilename());

        // MultipartFile içeriğini temp dosyaya kopyala
        Files.copy(multipartFile.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

        return tempFile.toFile();
    }
}

