package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDUzatmaKarariRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class IDUzatmaKarariDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDUzatmaKarariRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbHedeflerTalepService dbHedeflerTalepService;
    private final DbHedeflerDetayTalepService dbHedeflerDetayTalepService;
    private final DbHedeflerService dbHedeflerService;
    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    private final KararRequestMapper kararRequestMapper;

    public IDUzatmaKarariDBSaveHandler(DbMahkemeKararService dbMahkemeKararService,
                                       DbMahkemeKararTalepService dbMahkemeKararTalepService,
                                       DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService,
                                       DbHedeflerTalepService dbHedeflerTalepService,
                                       DbHedeflerDetayTalepService dbHedeflerDetayTalepService,
                                       DbHedeflerService dbHedeflerService,
                                       DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService,
                                       KararRequestMapper kararRequestMapper) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbHedeflerTalepService = dbHedeflerTalepService;
        this.dbHedeflerDetayTalepService = dbHedeflerDetayTalepService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbHedeflerAidiyatTalepService = dbHedeflerAidiyatTalepService;
        this.kararRequestMapper = kararRequestMapper;
    }

    @Override
    public void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDUzatmaKarariRequest> request) {

        validateRequest(request);

        IDUzatmaKarariRequest kararRequest = request.getKararRequest();

        LocalDateTime saveDateTime = LocalDateTime.now();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();

        IDUzatmaKarariRequestDetay uzatmaKarariRequestDetay = kararRequest.getUzatmaKarariRequestDetay();

        for (IDHedefDetay hedefDetay : CommonUtils.safeList(uzatmaKarariRequestDetay.getHedefDetayListesi())) {
            //processHedefDetay(hedefDetay, mahkemeKararTalepId, evrakId, kayitTarihi, kullaniciId);

            String hedefNo = hedefDetay.getHedefNoAdSoyad().getHedef().getHedefNo();
            HedefTip hedefTipi = hedefDetay.getHedefNoAdSoyad().getHedef().getHedefTip();

            MahkemeKararDetay ilgiliKararDetay = hedefDetay.getIlgiliMahkemeKararDetayi();
            MahkemeKarar iliskiliMahkemeKarar = dbMahkemeKararService.findBy(
                            ilgiliKararDetay.getMahkemeIlIlceKodu(),
                            ilgiliKararDetay.getMahkemeKodu(),
                            ilgiliKararDetay.getMahkemeKararNo(),
                            ilgiliKararDetay.getSorusturmaNo())
                    .orElseThrow(() -> new MakosResponseException(
                            String.format(MakosResponseErrorCodes.MK_BULUNAMADI,
                                    ilgiliKararDetay.getMahkemeIlIlceKodu(),
                                    ilgiliKararDetay.getMahkemeKodu(),
                                    ilgiliKararDetay.getMahkemeKararNo(),
                                    ilgiliKararDetay.getSorusturmaNo())
                    ));

            Hedefler iliskiliHedef = dbHedeflerService
                    .findByMahkemeKararIdAndHedefNoAndHedefTipi(
                            iliskiliMahkemeKarar.getId(),
                            hedefNo,
                            hedefTipi.getHedefKodu())
                    .orElseThrow(() -> new MakosResponseException(
                            MakosResponseErrorCodes.HEDEF_BULUNAMADI,
                            hedefNo,
                            String.valueOf(hedefTipi.getHedefKodu())));

            // Detay Mahkeme Karar Talep kaydı
            DetayMahkemeKararTalep detayKaydi = kararRequestMapper
                    .toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, request.getKullaniciId(), saveDateTime);
            DetayMahkemeKararTalep savedDetay = dbDetayMahkemeKararTalepService.save(detayKaydi);

            // Hedefler Detay Talep kaydı
            HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper
                    .toHedeflerDetayTalep(hedefDetay, mahkemeKararTalepId,
                            iliskiliHedef.getId(), request.getKullaniciId(), saveDateTime);
            hedeflerDetayTalep.setDetayMahkemeKararTalepId(savedDetay.getId());
            HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);

            // Hedefler Talep kaydı
            HedeflerTalep hedeflerTalep = kararRequestMapper
                    .toHedeflerTalep(hedefDetay, mahkemeKararTalepId, request.getKullaniciId(), saveDateTime);
            hedeflerTalep.setUzatmaId(iliskiliHedef.getId());
            HedeflerTalep savedHedeflerTalep = dbHedeflerTalepService.save(hedeflerTalep);

            // Aidiyat kayıtları
            saveHedeflerTalepAidiyatListesi(
                    hedefDetay.getHedefAidiyatKodlari(),
                    savedHedeflerTalep.getId(),
                    savedHedeflerTalep.getHedefNo(),
                    saveDateTime,
                    request.getKullaniciId());
        }
    }

    private void processHedefDetay(IDHedefDetay hedefDetay,
                                   Long mahkemeKararTalepId,
                                   Long evrakId,
                                   LocalDateTime kayitTarihi,
                                   Long kullaniciId) {

        String hedefNo = hedefDetay.getHedefNoAdSoyad().getHedef().getHedefNo();
        HedefTip hedefTipi = hedefDetay.getHedefNoAdSoyad().getHedef().getHedefTip();

        MahkemeKararDetay ilgiliKararDetay = hedefDetay.getIlgiliMahkemeKararDetayi();
        MahkemeKarar iliskiliMahkemeKarar = dbMahkemeKararService.findBy(
                        ilgiliKararDetay.getMahkemeIlIlceKodu(),
                        ilgiliKararDetay.getMahkemeKodu(),
                        ilgiliKararDetay.getMahkemeKararNo(),
                        ilgiliKararDetay.getSorusturmaNo())
                .orElseThrow(() -> new MakosResponseException(
                        String.format(MakosResponseErrorCodes.MK_BULUNAMADI,
                                ilgiliKararDetay.getMahkemeIlIlceKodu(),
                                ilgiliKararDetay.getMahkemeKodu(),
                                ilgiliKararDetay.getMahkemeKararNo(),
                                ilgiliKararDetay.getSorusturmaNo())
                ));

        Hedefler iliskiliHedef = dbHedeflerService
                .findByMahkemeKararIdAndHedefNoAndHedefTipi(
                        iliskiliMahkemeKarar.getId(),
                        hedefNo,
                        hedefTipi.getHedefKodu())
                .orElseThrow(() -> new MakosResponseException(
                        MakosResponseErrorCodes.HEDEF_BULUNAMADI,
                        hedefNo,
                        String.valueOf(hedefTipi.getHedefKodu())));

        // Detay Mahkeme Karar Talep kaydı
        DetayMahkemeKararTalep detayKaydi = kararRequestMapper
                .toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
        DetayMahkemeKararTalep savedDetay = dbDetayMahkemeKararTalepService.save(detayKaydi);

        // Hedefler Detay Talep kaydı
        HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper
                .toHedeflerDetayTalep(hedefDetay, mahkemeKararTalepId,
                        iliskiliHedef.getId(), kullaniciId, kayitTarihi);
        hedeflerDetayTalep.setDetayMahkemeKararTalepId(savedDetay.getId());
        HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);

        // Hedefler Talep kaydı
        HedeflerTalep hedeflerTalep = kararRequestMapper
                .toHedeflerTalep(hedefDetay, mahkemeKararTalepId, kullaniciId, kayitTarihi);
        hedeflerTalep.setUzatmaId(iliskiliHedef.getId());
        HedeflerTalep savedHedeflerTalep = dbHedeflerTalepService.save(hedeflerTalep);

        // Aidiyat kayıtları
        saveHedeflerTalepAidiyatListesi(
                hedefDetay.getHedefAidiyatKodlari(),
                savedHedeflerTalep.getId(),
                savedHedeflerTalep.getHedefNo(),
                kayitTarihi,
                kullaniciId);
    }

    private List<HedeflerAidiyatTalep> saveHedeflerTalepAidiyatListesi(List<String> aidiyatListesi,
                                                                       Long hedeflerTalepId,
                                                                       String hedefNo,
                                                                       LocalDateTime islemTarihi,
                                                                       Long kullaniciId) {
        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : CommonUtils.safeList(aidiyatListesi)) {
            HedeflerAidiyatTalep entity = HedeflerAidiyatTalep.builder()
                    .hedefTalepId(hedeflerTalepId)
                    .aidiyatKod(aidiyatKodu)
                    .tarih(islemTarihi)
                    .kullaniciId(kullaniciId)
                    .build();

            HedeflerAidiyatTalep saved = dbHedeflerAidiyatTalepService.save(entity);
            if (saved == null) {
                throw new MakosResponseException(
                        MakosResponseErrorCodes.MKTALEP_HEDEFLER_AIDIYAT_KAYDETMEHATASI,
                        hedefNo, aidiyatKodu);
            }
            result.add(saved);
        }

        return result;
    }
}


