package iym.makos.model.dto.mktalep.dbsave;

import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@Builder
@ToString
@EqualsAndHashCode
public class MkTalepDbSaveRequest<T extends MkTalepRequest> {

    @NotNull
    private T kararRequest;

    @NotNull
    private Long kullaniciId;

    public ValidationResult validate() {

        ValidationResult result = new ValidationResult(true);

        if (kararRequest == null) {
            result.addFailedReason(CommonUtils.getFormattedString(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "request boş olamaz"));
            return result;
        }

        if (kullaniciId == null) {
            result.addFailedReason(CommonUtils.getFormattedString(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kullaniciId boş olamaz"));
            return result;
        }

        ValidationResult valid = kararRequest.isValid();
        if (!valid.isValid()) {
            return valid;
        }

        return result;

    }

}
