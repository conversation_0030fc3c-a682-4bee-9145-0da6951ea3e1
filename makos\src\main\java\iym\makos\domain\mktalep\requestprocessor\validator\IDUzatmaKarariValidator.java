package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.HedefTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDUzatmaKarariRequestDetay;
import iym.makos.model.dto.mktalep.request.id.detay.IDYeniKararRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Optional;

@Service
@Slf4j
public class IDUzatmaKarariValidator extends MahkemeKararRequestValidatorBase<IDUzatmaKarariRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbHedeflerService dbHedeflerService;
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public IDUzatmaKarariValidator(DbMahkemeKararService dbMahkemeKararService
            , DbHedeflerService dbHedeflerService
            , DbSucTipiService dbSucTipiService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    protected ValidationResult doValidate(IDUzatmaKarariRequest request) {

        try {
            ValidationResult validationResult = new ValidationResult(true);

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);
            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();

            //Ohal Kontrolu
            if (LocalDate.now().isAfter(IDYeniKararValidator.OHAL_DATE) && kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)) {
                validationResult.addFailedReason("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
            }

            IDUzatmaKarariRequestDetay kararRequestDetay = request.getUzatmaKarariRequestDetay();

            if (kararRequestDetay == null) {
                validationResult.addFailedReason("IDUzatmaKarariRequestDetay boş olamaz");
                return validationResult;
            }

            for (IDHedefDetay IDHedefDetay : kararRequestDetay.getHedefDetayListesi()) {

                if (!CommonUtils.isNullOrEmpty(IDHedefDetay.getCanakNo())) {
                    validationResult.addFailedReason("Uzatma kararında CANAK numarası girilemez.");
                }

                if (IDHedefDetay.getUzatmaSayisi() == null) {
                    validationResult.addFailedReason("Uzatma Kararinda uzatma sayisi boş olamaz!");
                }

                MahkemeKararDetay iliskiliMahkemeKararDetay = IDHedefDetay.getIlgiliMahkemeKararDetayi();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkili mahkeme karar boş olamaz.!");
                } else {

                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu(), iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {

                        MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();
                        String hedefNo = IDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefNo();
                        HedefTip hedefTipi = IDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefTip();

                        //Eski karar uzatmaya uygun olup olmadigi burada mi kontrol edilecek
                        Optional<Hedefler> existingHedefBilgisiOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, hedefTipi.getHedefKodu());

                        if (existingHedefBilgisiOpt.isEmpty()) {
                            validationResult.addFailedReason(hedefNo + " numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
                        }
                    }
                }
            }

            Optional.ofNullable(kararRequestDetay.getMahkemeSucTipiKodlari())
                    .orElse(Collections.emptyList())
                    .forEach(sucTipiKodu -> {
                        Optional<SucTipi> sucTipiOpt = dbSucTipiService.findBySucTipiKodu(sucTipiKodu);
                        if (sucTipiOpt.isEmpty()) {
                            validationResult.addFailedReason(String.format("'%s' kodu ile eşleşen bir suç tipi mevcut değil.", sucTipiKodu));
                        }
                    });


            Optional.ofNullable(kararRequestDetay.getMahkemeAidiyatKodlari())
                    .orElse(Collections.emptyList())
                    .forEach(aidiyatKod -> {
                        String uyariMesaj = CommonUtils.aidiyatEklemeUyarisi(aidiyatKod, kararTipi, evrakKurum);
                        if (!CommonUtils.isNullOrEmpty(uyariMesaj)) {
                            validationResult.addFailedReason(uyariMesaj);
                        }
                    });

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI;
    }

}

