package iym.makos.model.dto.mktalep;

import iym.makos.model.MakosRequestResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Data
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Jacksonized
public class MkTalepResponse extends MakosRequestResponse {

}
