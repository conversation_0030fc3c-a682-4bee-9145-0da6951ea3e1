package iym.makos.model.dto.evrak;

import iym.makos.model.MakosRequestResponse;
import iym.makos.model.dto.evrak.view.IslenecekEvrakViewInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.data.domain.Page;

import java.util.List;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class IslenecekEvrakResponse extends MakosRequestResponse {
    private Page<IslenecekEvrakViewInfo> islenecekEvraklar;
} 