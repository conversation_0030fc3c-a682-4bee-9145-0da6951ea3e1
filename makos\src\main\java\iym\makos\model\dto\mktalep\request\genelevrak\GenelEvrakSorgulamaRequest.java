package iym.makos.model.dto.mktalep.request.genelevrak;

import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguParam;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import iym.makos.model.MakosQueryRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class GenelEvrakSorgulamaRequest extends MakosQueryRequest {

    @NotNull
    MahkemeKararTalepSorguParam sorguParam;

}

