package iym.makos.domain.mk.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.mk.*;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mk.*;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDSonlandirmaKararDetay;
import iym.makos.model.dto.view.info.*;
import iym.makos.model.dto.view.mapper.HedeflerIslemEnricherMapper;
import iym.makos.model.dto.view.mapper.KararAidiyatIslemEnricherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class IDSonlandirmaEnricher implements MKEnricher {

    private final DbHedeflerService dbHedeflerIslemService;
    private final HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper;
    private final DbHedeflerAidiyatService dbHedeflerAidiyatService;
    private final DbMahkemeAidiyatService dbMahkemeAidiyatService;
    private final KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper;
    private final DbMahkemeSuclarService dbMahkemeSuclarService;
    private final DbSucTipiService dbSucTipiService;

    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    private final DbHedeflerDetayService dbHedeflerDetayService;
    private final DbDetayMahkemeKararService dbDetayMahkemeKararService;

    @Autowired
    public IDSonlandirmaEnricher(
            DbHedeflerService dbHedeflerIslemService
            , HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper
            , DbHedeflerAidiyatService dbHedeflerAidiyatService
            , DbMahkemeAidiyatService dbMahkemeAidiyatService
            , KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper
            , DbMahkemeSuclarService dbMahkemeSuclarService
            , DbSucTipiService dbSucTipiService
            , DbHedeflerDetayService dbHedeflerDetayService
            , DbDetayMahkemeKararService dbDetayMahkemeKararService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService

    ) {
        this.dbHedeflerIslemService = dbHedeflerIslemService;
        this.hedeflerIslemEnricherMapper = hedeflerIslemEnricherMapper;
        this.dbHedeflerAidiyatService = dbHedeflerAidiyatService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;
        this.iDKararAidiyatIslemEnricherMapper = iDKararAidiyatIslemEnricherMapper;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbSucTipiService = dbSucTipiService;
        this.dbHedeflerDetayService = dbHedeflerDetayService;
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKarar) {
        try {
            Long mahkemeKararId = mahkemeKarar.getMahkemeKararId();
            if (mahkemeKararId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararId yok");
            }

            //enrich only IDYeniKararDetay

            //Kararin Hedefler Listesi
            List<Hedefler> hedeflerList = dbHedeflerIslemService.findByMahkemeKararId(mahkemeKararId);
            List<SonlandirmaKarariHedefInfo> sonlandirmaKarariHedefList = enrichHedefler(hedeflerList);

            //Kararin Aidiyat Listsi
            List<MahkemeAidiyat> aidiyatListesi = dbMahkemeAidiyatService.findByMahkemeKararId(mahkemeKararId);
            List<IDKarariAidiyatInfo> iDKarariAidiyatInfoList = enrichAidiyatListesi(aidiyatListesi);

            //Kararin Suc Tipleri
            List<MahkemeSuclar> sucLitesi = dbMahkemeSuclarService.findByMahkemeKararId(mahkemeKararId);
            List<IDKarariSucTipiInfo> sucTipleriDTOList = enrichSucTipleri(sucLitesi, mahkemeKararId);

            //IDMahkemeKarariInfo'in enrich edilecek nesnesi : MkIDYeniKararDTO
            IDSonlandirmaKararDetay sonlandirmaKararDetay = IDSonlandirmaKararDetay.builder()
                    .hedefListesi(sonlandirmaKarariHedefList)
                    .aidiyatListesi(iDKarariAidiyatInfoList)
                    .sucTipleri(sucTipleriDTOList)
                    .build();

            mahkemeKarar.setIDSonlandirmaKararDetay(sonlandirmaKararDetay);

            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("IDSonlandirmaEnricher failed. mahkemeKararId:{}", mahkemeKarar.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }

    private List<IDKarariSucTipiInfo> enrichSucTipleri(List<MahkemeSuclar> kararSucTipleri, Long mahkemeKararId) {
        List<IDKarariSucTipiInfo> result = new ArrayList<>();
        List<SucTipi> sucTipiListesi = dbSucTipiService.getByMahkemeId(mahkemeKararId);
        CommonUtils.safeList(kararSucTipleri).forEach(mahkemeKararSucTipi -> {

            SucTipi sucTipi = sucTipiListesi.stream()
                    .filter(s -> s.getSucTipiKodu().equals(mahkemeKararSucTipi.getSucTipKodu()))
                    .findFirst()
                    .orElse(null);

            SucTipiInfo sucTipiInfo = SucTipiInfo.builder()
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipAdi(sucTipi != null ? sucTipi.getAciklama() : "")
                    .build();

            IDKarariSucTipiInfo iDKarariSucTipiInfo = IDKarariSucTipiInfo.builder()
                    .id(mahkemeKararSucTipi.getId())
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipi(sucTipiInfo)
                    .mahkemeKararId(mahkemeKararId)
                    .build();

            result.add(iDKarariSucTipiInfo);

        });

        return result;
    }

    private List<SonlandirmaKarariHedefInfo> enrichHedefler(List<Hedefler> list) {
        List<SonlandirmaKarariHedefInfo> result = new ArrayList<>();
        list.forEach(hedefler -> {

            DetayMahkemeKarar detayMahkemeKarar = null;
            Optional<HedeflerDetay> hedeflerDetayOpt = dbHedeflerDetayService.findHedeflerDetayIslem(hedefler.getMahkemeKararId(), hedefler.getHedefNo(), hedefler.getHedefTipi());
            if (hedeflerDetayOpt.isPresent()) {
                Long detayMahkemeKararId = hedeflerDetayOpt.get().getDetayMahkemeKararId();
                Optional<DetayMahkemeKarar> detayMahkemeKararOpt = dbDetayMahkemeKararService.findById(detayMahkemeKararId);
                if (detayMahkemeKararOpt.isPresent()) {
                    detayMahkemeKarar = detayMahkemeKararOpt.get();
                }
            }
            DetayMahkemeKararInfo iliskiliMahkemeKararDetay = getMahkemeKararDetayInfo(detayMahkemeKarar);

            //Hedefin aidiyat listesi.
            List<HedeflerAidiyat> hedeflerAidiyatList = dbHedeflerAidiyatService.findByHedeflerId(hedefler.getId());
            List<IDHedefAidiyatInfo> aidiyatList = hedeflerIslemEnricherMapper.toIDHedefAidiyatList(hedeflerAidiyatList);

            SonlandirmaKarariHedefInfo sonlandirmaKarariHedefInfo = hedeflerIslemEnricherMapper.toSonlandirmaKarariHedefInfo(hedefler);
            sonlandirmaKarariHedefInfo.setIliskiliMahkemeKararInfo(iliskiliMahkemeKararDetay);
            sonlandirmaKarariHedefInfo.setHedefAidiyatListesi(aidiyatList);

            result.add(sonlandirmaKarariHedefInfo);
        });

        return result;
    }

    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKarar detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

    private List<IDKarariAidiyatInfo> enrichAidiyatListesi(List<MahkemeAidiyat> aidiyatListesi) {
        return aidiyatListesi.stream()
                .map(iDKararAidiyatIslemEnricherMapper::toIDKarariAidiyatInfo)
                .collect(Collectors.toList());

    }

}



