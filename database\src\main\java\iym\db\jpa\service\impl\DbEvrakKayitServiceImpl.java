package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.service.db.DbEvrakKayitService;
import iym.db.jpa.dao.EvrakKayitRepo;
import oracle.jdbc.OracleDatabaseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * Service implementation for EvrakKayit entity
 */
@Service
public class DbEvrakKayitServiceImpl extends GenericDbServiceImpl<EvrakKayit, Long> implements DbEvrakKayitService {

    private final EvrakKayitRepo evrakKayitRepo;

    @Autowired
    public DbEvrakKayitServiceImpl(EvrakKayitRepo repository) {
        super(repository);
        this.evrakKayitRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByEvrakSiraNo(String evrakSiraNo) {
        return evrakKayitRepo.existsByEvrakSiraNo(evrakSiraNo);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EvrakKayit> findByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo
            , String evrakGeldigiKurumKodu){
        return evrakKayitRepo.findByEvrakNoAndEvrakGeldigiKurumKodu(evrakNo, evrakGeldigiKurumKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findAllByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo
            , String evrakGeldigiKurumKodu){
        return evrakKayitRepo.findAllByEvrakNoAndEvrakGeldigiKurumKodu(evrakNo, evrakGeldigiKurumKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EvrakKayit> findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(String evrakNo,
                                                                                   String gelIlIlceKodu,
                                                                                   String evrakGeldigiKurumKodu){
        return evrakKayitRepo.findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(evrakNo, gelIlIlceKodu, evrakGeldigiKurumKodu);
    }


    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByEvrakTipi(String evrakTipi) {
        return evrakKayitRepo.findByEvrakTipi(evrakTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByGirisTarihBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return evrakKayitRepo.findByGirisTarihBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByDurumu(String durumu) {
        return evrakKayitRepo.findByDurumu(durumu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByHavaleBirim(String havaleBirim) {
        return evrakKayitRepo.findByHavaleBirim(havaleBirim);
    }



    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByAcilmi(String acilmi) {
        return evrakKayitRepo.findByAcilmi(acilmi);
    }


    @Override
    @Transactional(readOnly = true)
    public Page<EvrakKayit> findIslenecekEvraklarByKurumKodu(String kurumKodu, Pageable p){
        Page<EvrakKayit> result = null;
        String error = "";
        try {

            List<EvrakKayit> list = evrakKayitRepo.findByEvrakGeldigiKurumKoduAndDurumuIsNull(kurumKodu);

            result = new PageImpl<>(
                    list,
                    p,
                    100
            );


        }catch (DataIntegrityViolationException de){
            error = "DataIntegrityViolationException: " + de.getMessage();
        }
        catch (DataAccessException dae) {
            error = "DataAccessException: " + dae.getMessage();
            // Diğer tüm veritabanı hataları
            //throw new Exception("Veritabanı erişim hatası oluştu.", ex);
        }
        catch (Exception ex) {
            error = "DataAccessException: " + ex.getMessage();
            // Diğer tüm veritabanı hataları
            //throw new Exception("Veritabanı erişim hatası oluştu.", ex);
        }

        return  result;
    }


}
