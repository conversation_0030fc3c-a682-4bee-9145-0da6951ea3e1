package iym.makos.domain.mkislem.enrich;

import iym.common.enums.HedefTip;
import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.mkislem.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.mkislem.HedeflerDetayIslem;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mkislem.DbDetayMahkemeKararIslemService;
import iym.common.service.db.mkislem.DbHedeflerDetayIslemService;
import iym.common.util.CommonUtils;
import iym.makos.model.api.HedefGuncellemeAlan;
import iym.makos.model.dto.view.IDHedefGuncellemeKararDetay;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.HedefGuncellemeAlanInfo;
import iym.makos.model.dto.view.info.HedefGuncellemeInfo;
import iym.makos.model.dto.view.info.HedeflerDetayDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class HedefGuncellemeIslemEnricher implements MKIslemEnricher {

    private final DbDetayMahkemeKararIslemService dbDetayMahkemeKararService;
    private DbHedeflerDetayIslemService dbHedeflerDetayService;
    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public HedefGuncellemeIslemEnricher(DbDetayMahkemeKararIslemService dbDetayMahkemeKararService
            , DbHedeflerDetayIslemService dbHedeflerDetayService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbHedeflerDetayService = dbHedeflerDetayService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    /*
    Bir HedefGuncellemeKarari ile daha onceden onaylanmis bir yada birden fazla mahkeme kararina ait hedeflerin güncellenmesi yapilmaktadir.
    Bu  yuzden bir islem id icin birden fazla dmahkemekararislem satiri bulunur. Bu satirlarin her birisi mahkeme karari temsil eder.
    Her bir satira ait ise bir yada birden fazla hedef guncelleme bilgisi bulunur.
    * */

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {

        try {

            Long mahkemeKararIslemId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararIslemId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararIslemId yok");
            }

            List<HedefGuncellemeInfo> hedefGuncellemeInfoList = new ArrayList<>();

            List<DetayMahkemeKararIslem> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKararIslem -> {

                DetayMahkemeKararInfo detayMahkemeKararInfo = getMahkemeKararDetayInfo(detayMahkemeKararIslem);

                //Hedef Guncellemede her bir DetayMahkemeKararInfo icin liste seklinde guncellme bilgisi bulunmaktadir.
                HedefGuncellemeInfo hedefGuncellemeInfo =  HedefGuncellemeInfo.builder()
                        .detayMahkemeKararInfo(detayMahkemeKararInfo)
                        .build();
                List<HedeflerDetayDTO> hedeflerDetayDTOList = new ArrayList<>();

                //Bir detaya ait birden fazla hedef icin guncelleme yapilir.
                List<HedeflerDetayIslem> hedeflerDetayIslemList = dbHedeflerDetayService.findByDetayMahkemeKararIslemId(detayMahkemeKararIslem.getId());
                for(HedeflerDetayIslem hedeflerDetayIslem : CommonUtils.safeList(hedeflerDetayIslemList)){

                    String hedefNo = hedeflerDetayIslem.getHedefNo();
                    HedefTip hedefTipi = HedefTip.fromValue(hedeflerDetayIslem.getHedefTipi());

                    HedefGuncellemeAlanInfo adiGuncellemeAlani = null;
                    HedefGuncellemeAlanInfo soyadiGuncellemeAlani = null;
                    HedefGuncellemeAlanInfo tcknGuncellemeAlani = null;
                    HedefGuncellemeAlanInfo canakNoGuncellemeAlani = null;

                    //TODO: HedefGuncellemeAlanInfo alana eski degerin de yazilmas iyi olur
                    if (CommonUtils.safeString(hedeflerDetayIslem.getUpdateColumnNames()).contains(HedefGuncellemeAlan.AD.name())) {
                        adiGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.AD)
                                .yeniDegeri(hedeflerDetayIslem.getHedefAdi())
                                .build();

                    } else if (CommonUtils.safeString(hedeflerDetayIslem.getUpdateColumnNames()).contains(HedefGuncellemeAlan.SOYAD.name())) {
                        soyadiGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.SOYAD)
                                .yeniDegeri(hedeflerDetayIslem.getHedefSoyadi())
                                .build();
                    } else if (CommonUtils.safeString(hedeflerDetayIslem.getUpdateColumnNames()).contains(HedefGuncellemeAlan.TCKIMlIKNO.name())) {
                        tcknGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.TCKIMlIKNO)
                                .yeniDegeri(hedeflerDetayIslem.getTcKimlikNo())
                                .build();
                    } else if (CommonUtils.safeString(hedeflerDetayIslem.getUpdateColumnNames()).contains(HedefGuncellemeAlan.CANAK_NO.name())) {
                        canakNoGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.CANAK_NO)
                                .yeniDegeri(hedeflerDetayIslem.getCanakNo())
                                .build();
                    }


                    HedeflerDetayDTO hedeflerDetayDTO = HedeflerDetayDTO.builder()
                            .detayMahkemeKararId(detayMahkemeKararIslem.getId())
                            .id(hedeflerDetayIslem.getId())
                            .hedefNo(hedefNo)
                            .hedefTipi(hedefTipi)
                            .kayitTarihi(hedeflerDetayIslem.getKayitTarihi())
                            .durumu(hedeflerDetayIslem.getDurumu())
                            .adiGuncellemeAlani(adiGuncellemeAlani)
                            .soyadiGuncellemeAlani(soyadiGuncellemeAlani)
                            .tcknGuncellemeAlani(tcknGuncellemeAlani)
                            .canakNoGuncellemeAlani(canakNoGuncellemeAlani)
                            .detayMahkemeKararId(detayMahkemeKararIslem.getId())
                            .build();

                    hedeflerDetayDTOList.add(hedeflerDetayDTO);
                }

                hedefGuncellemeInfo.setHedeflerDetayList(hedeflerDetayDTOList);
                hedefGuncellemeInfoList.add(hedefGuncellemeInfo);

            });

            IDHedefGuncellemeKararDetay hedefGuncellemeKararDetay = IDHedefGuncellemeKararDetay.builder()
                    .guncellemeListesi(hedefGuncellemeInfoList)
                    .build();

            mahkemeKararIslem.setIDHedefGuncellemeKararDetay(hedefGuncellemeKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("HedefGuncellemeEnricher failed. mahkemeKararIslemId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }

    }

    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararIslem detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        DetayMahkemeKararInfo result = DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(!ilIlceOpt.isEmpty() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(!mahkemeBilgiOpt.isEmpty() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
        return result;
    }

}



