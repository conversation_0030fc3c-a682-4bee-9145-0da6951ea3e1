package iym.makos.domain.mktalep.enrich;

import iym.common.enums.HedefTip;
import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.util.CommonUtils;
import iym.makos.model.api.HedefGuncellemeAlan;
import iym.makos.model.dto.view.IDHedefGuncellemeKararDetay;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.HedefGuncellemeAlanInfo;
import iym.makos.model.dto.view.info.HedefGuncellemeInfo;
import iym.makos.model.dto.view.info.HedeflerDetayDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class HedefGuncellemeTalepEnricher implements MKTalepEnricher {

    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararService;
    private DbHedeflerDetayTalepService dbHedeflerDetayService;
    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public HedefGuncellemeTalepEnricher(DbDetayMahkemeKararTalepService dbDetayMahkemeKararService
            , DbHedeflerDetayTalepService dbHedeflerDetayService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbHedeflerDetayService = dbHedeflerDetayService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {

        try {

            Long mahkemeKararTalepId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararTalepId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararTalepId yok");
            }

            List<HedefGuncellemeInfo> hedefGuncellemeInfoList = new ArrayList<>();

            List<DetayMahkemeKararTalep> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKarar -> {

                DetayMahkemeKararInfo detayMahkemeKararInfo = getMahkemeKararDetayInfo(detayMahkemeKarar);

                //Hedef Guncellemede her bir DetayMahkemeKararInfo icin liste seklinde guncellme bilgisi bulunmaktadir.
                HedefGuncellemeInfo hedefGuncellemeInfo =  HedefGuncellemeInfo.builder()
                        .detayMahkemeKararInfo(detayMahkemeKararInfo)
                        .build();
                List<HedeflerDetayDTO> hedeflerDetayDTOList = new ArrayList<>();

                //Bir detaya ait birden fazla hedef icin guncelleme yapilir.
                List<HedeflerDetayTalep> hedeflerDetayIslemList = dbHedeflerDetayService.findByMahkemeKararTalepId(detayMahkemeKarar.getId());
                for(HedeflerDetayTalep hedeflerDetay : CommonUtils.safeList(hedeflerDetayIslemList)){

                    String hedefNo = hedeflerDetay.getHedefNo();
                    HedefTip hedefTipi = HedefTip.fromValue(hedeflerDetay.getHedefTipi());

                    HedefGuncellemeAlanInfo adiGuncellemeAlani = null;
                    HedefGuncellemeAlanInfo soyadiGuncellemeAlani = null;
                    HedefGuncellemeAlanInfo tcknGuncellemeAlani = null;
                    HedefGuncellemeAlanInfo canakNoGuncellemeAlani = null;

                    if (CommonUtils.safeString(hedeflerDetay.getUpdateColumnNames()).contains(HedefGuncellemeAlan.AD.name())) {
                        adiGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.AD)
                                .yeniDegeri(hedeflerDetay.getHedefAdi())
                                .build();

                    } else if (CommonUtils.safeString(hedeflerDetay.getUpdateColumnNames()).contains(HedefGuncellemeAlan.SOYAD.name())) {
                        soyadiGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.SOYAD)
                                .yeniDegeri(hedeflerDetay.getHedefSoyadi())
                                .build();
                    } else if (CommonUtils.safeString(hedeflerDetay.getUpdateColumnNames()).contains(HedefGuncellemeAlan.TCKIMlIKNO.name())) {
                        tcknGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.TCKIMlIKNO)
                                .yeniDegeri(hedeflerDetay.getTcKimlikNo())
                                .build();
                    } else if (CommonUtils.safeString(hedeflerDetay.getUpdateColumnNames()).contains(HedefGuncellemeAlan.CANAK_NO.name())) {
                        canakNoGuncellemeAlani = HedefGuncellemeAlanInfo.builder()
                                .hedefGuncellemeAlanTuru(HedefGuncellemeAlan.CANAK_NO)
                                .yeniDegeri(hedeflerDetay.getCanakNo())
                                .build();
                    }


                    HedeflerDetayDTO hedeflerDetayDTO = HedeflerDetayDTO.builder()
                            .detayMahkemeKararId(detayMahkemeKarar.getId())
                            .id(hedeflerDetay.getId())
                            .hedefNo(hedefNo)
                            .hedefTipi(hedefTipi)
                            .kayitTarihi(hedeflerDetay.getKayitTarihi())
                            .durumu(hedeflerDetay.getDurumu())
                            .adiGuncellemeAlani(adiGuncellemeAlani)
                            .soyadiGuncellemeAlani(soyadiGuncellemeAlani)
                            .tcknGuncellemeAlani(tcknGuncellemeAlani)
                            .canakNoGuncellemeAlani(canakNoGuncellemeAlani)
                            .detayMahkemeKararId(detayMahkemeKarar.getId())
                            .build();

                    hedeflerDetayDTOList.add(hedeflerDetayDTO);
                }

                hedefGuncellemeInfo.setHedeflerDetayList(hedeflerDetayDTOList);
                hedefGuncellemeInfoList.add(hedefGuncellemeInfo);

            });

            IDHedefGuncellemeKararDetay hedefGuncellemeKararDetay = IDHedefGuncellemeKararDetay.builder()
                    .guncellemeListesi(hedefGuncellemeInfoList)
                    .build();

            mahkemeKararIslem.setIDHedefGuncellemeKararDetay(hedefGuncellemeKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("HedefGuncellemeTalepEnricher failed. mahkemeKararTalepId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }

    }

    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararTalep detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

}



