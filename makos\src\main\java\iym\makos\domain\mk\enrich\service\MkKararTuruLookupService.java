package iym.makos.domain.mk.enrich.service;

import iym.common.enums.EvrakTuru;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.EvrakKayitRepo;
import iym.db.jpa.dao.mktalep.MahkemeKararTalepRepo;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mk.query.kararturu.MahkemeKararTuruResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Service
@RequiredArgsConstructor
@Slf4j
public class MkKararTuruLookupService {

    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private final EvrakKayitRepo evrakKayitRepo;

    public MahkemeKararTuruResponse findKararTuru(long mahkemeKararId) {
        log.debug("Looking up KararTuru for mahkemeKararId: {}", mahkemeKararId);
        
        // Get MahkemeKarar by ID
        Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararId);
        if (mahkemeKararTalepOpt.isEmpty()) {
            log.warn("MahkemeKararIslem not found for ID: {}", mahkemeKararId);
            return MahkemeKararTuruResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("MahkemeKararTalep bulunamadi. kararId: " + mahkemeKararId)
                            .build())
                    .build();
        }

        MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepOpt.get();
        
        // Get the associated EvrakKayit to determine EvrakTuru
        Optional<EvrakKayit> evrakKayitOpt = evrakKayitRepo.findById(mahkemeKararTalep.getEvrakId());
        if (evrakKayitOpt.isEmpty()) {
            log.warn("EvrakKayit not found for evrakId: {}", mahkemeKararTalep.getEvrakId());
            return MahkemeKararTuruResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("EvrakKayit bulunamadi. evrakId: " + mahkemeKararTalep.getEvrakId())
                            .build())
                    .build();
        }

        EvrakKayit evrakKayit = evrakKayitOpt.get();
        KararTuru kararTuru = null;

        // Logic based on MkTalepStateUpdaterService.determineKararTuru()
        if (!CommonUtils.isNullOrEmpty(evrakKayit.getEvrakYonu())) {
            EvrakTuru evrakTuru = EvrakTuru.fromName(evrakKayit.getEvrakYonu());

            if (evrakTuru == EvrakTuru.GENEL_EVRAK) {
                kararTuru = KararTuru.GENEL_EVRAK;
            } else if (evrakTuru == EvrakTuru.ILETISIMIN_TESPITI) {
                kararTuru = KararTuru.ILETISIMIN_TESPITI;
            } else { // ILETISIMIN_DENETLENMESI
                // For other EvrakTuru types, determine from kararTip
                if (!CommonUtils.isNullOrEmpty(mahkemeKararTalep.getKararTip())) {
                    try {
                        int kararTipiInt = Integer.parseInt(mahkemeKararTalep.getKararTip());
                        MahkemeKararTip kararTipi = MahkemeKararTip.fromValue(kararTipiInt);
                        kararTuru = CommonUtils.getKararTuru(kararTipi);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid kararTip format for kararId {}: {}", mahkemeKararId, mahkemeKararTalep.getKararTip());
                        return MahkemeKararTuruResponse.builder()
                                .response(MakosApiResponse.builder()
                                        .responseCode(MakosResponseCode.FAILED)
                                        .responseMessage("Yanlis kararTip formati: " + mahkemeKararTalep.getKararTip())
                                        .build())
                                .build();
                    } catch (IllegalArgumentException e) {
                        log.warn("Unknown kararTip for kararId {}: {}", mahkemeKararId, mahkemeKararTalep.getKararTip());
                        return MahkemeKararTuruResponse.builder()
                                .response(MakosApiResponse.builder()
                                        .responseCode(MakosResponseCode.FAILED)
                                        .responseMessage("Bilinmeyen kararTip: " + mahkemeKararTalep.getKararTip())
                                        .build())
                                .build();
                    }
                }
            }
        }

        if (kararTuru == null) {
            log.warn("Could not determine KararTuru for mahkemeKararTalep: {}", mahkemeKararId);
            return MahkemeKararTuruResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("KararTuru bulunamadi. mahkemeKararIslem: " + mahkemeKararId)
                            .build())
                    .build();
        }

        log.debug("Determined KararTuru {} for kararId: {}", kararTuru, mahkemeKararId);
        return MahkemeKararTuruResponse.builder()
                .kararTuru(kararTuru)
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.SUCCESS)
                        .build())
                .build();
    }
}
