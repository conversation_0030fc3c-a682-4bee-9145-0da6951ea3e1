package iym.common.model.entity.iym.talep;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;

/**
 * Entity class for MAHKEME_KARAR_TALEP_DETAY table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKaraTalepOzellik")
@Table(name = "MAHKEME_KARAR_TALEP_OZELLIK")
public class MahkemeKararTalepOzellik implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "MAHKEME_KARAR_TALEP_ID")
    private Long mahkemeKararTalepId;

    @Column(name = "KARAR_TURU", length = 50)
    @Size(max = 50)
    private String kararTuru;

    @Column(name = "REQUEST_ID", nullable = false)
    private UUID requestId;


}
