package iym.makos.model.dto.mktalep.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.KararTuru;
import iym.makos.model.MakosRequest;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.api.MahkemeKararBilgisi;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.UUID;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode
public abstract class MkTalepRequest implements MakosRequest {

    public MkTalepRequest() {
        assignKararTuru();
    }

    @NotNull
    protected UUID id;

    @NotNull
    protected KararTuru kararTuru;

    @NotNull
    @Valid
    protected EvrakDetay evrakDetay;

    @NotNull
    @Valid
    @Schema(description = "Mahkeme karar bilgileri")
    protected MahkemeKararBilgisi mahkemeKararBilgisi;

    //internal amacli kullanilacaktir.
    @JsonIgnore
    private String fileName;

    protected abstract void assignKararTuru();
}
