package iym.makos.domain.mktalep.enrich.service;


import iym.common.enums.KararTuru;
import iym.common.model.api.Response;
import iym.makos.domain.mktalep.enrich.MKTalepEnricher;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MkTalepEnricherFactory {

    private final Map<KararTuru, MKTalepEnricher> enricherMap = new HashMap<>();

    @Autowired
    public MkTalepEnricherFactory(List<MKTalepEnricher> enricherList) {
        for (MKTalepEnricher enricher : enricherList) {
            enricherMap.put(enricher.getSupportedKararTuru(), enricher);
        }
    }

    public Response<String> enrich(MahkemeKarariInfo mahkemeKarariInfo) {
        MKTalepEnricher enricher = enricherMap.get(mahkemeKarariInfo.getKararTuru());
        return enricher.enrich(mahkemeKarariInfo);
    }

    public Response<String>  enrich(MahkemeKarariInfo mahkemeKarariInfo, KararTuru kararTuru) {
        MKTalepEnricher enricher = enricherMap.get(kararTuru);
        return enricher.enrich(mahkemeKarariInfo);
    }
}
