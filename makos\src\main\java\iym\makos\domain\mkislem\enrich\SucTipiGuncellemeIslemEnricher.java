package iym.makos.domain.mkislem.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.mkislem.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.mkislem.MahkemeSucTipiDetayIslem;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mkislem.DbDetayMahkemeKararIslemService;
import iym.common.service.db.mkislem.DbHedeflerDetayIslemService;
import iym.common.service.db.mkislem.DbMahkemeSucTipiDetayIslemService;
import iym.common.util.CommonUtils;
import iym.makos.mapper.HedeflerInfoMapper;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDSucTipiGuncellemeKararDetay;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.IDKararSucTipiGuncellemeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class SucTipiGuncellemeIslemEnricher implements MKIslemEnricher {

    private final HedeflerInfoMapper hedeflerInfoMapper;
    private final DbHedeflerDetayIslemService dbHedeflerDetayService;
    private final DbDetayMahkemeKararIslemService dbDetayMahkemeKararService;
    private final DbMahkemeSucTipiDetayIslemService dbMahkemeSucTipiDetayService;
    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public SucTipiGuncellemeIslemEnricher(HedeflerInfoMapper hedeflerInfoMapper
            , DbHedeflerDetayIslemService dbHedeflerDetayService
            , DbDetayMahkemeKararIslemService dbDetayMahkemeKararService
            , DbMahkemeSucTipiDetayIslemService dbMahkemeSucTipiDetayService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.hedeflerInfoMapper = hedeflerInfoMapper;
        this.dbHedeflerDetayService = dbHedeflerDetayService;
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbMahkemeSucTipiDetayService = dbMahkemeSucTipiDetayService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }


    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {
        try {

            Long mahkemeKararIslemId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararIslemId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararIslemId yok");
            }

            List<IDKararSucTipiGuncellemeInfo> sucTipiGuncellemeListesi = new ArrayList<>();

            List<DetayMahkemeKararIslem> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKarar -> {

                DetayMahkemeKararInfo detayMahkemeKararInfo = getMahkemeKararDetayInfo(detayMahkemeKarar);

                List<MahkemeSucTipiDetayIslem> list = dbMahkemeSucTipiDetayService.findByMahkemeKararDetayIslemId(detayMahkemeKarar.getId());

                IDKararSucTipiGuncellemeInfo iDKararSucTipiGuncellemeInfo = IDKararSucTipiGuncellemeInfo.builder()
                        .detayMahkemeKararInfo(detayMahkemeKararInfo)
                        //.guncellenecekKararBilgisi(mahkemeKararDetayInfo)
                        .build();

                sucTipiGuncellemeListesi.add(iDKararSucTipiGuncellemeInfo);

            });


            //Enrich edilecek nesne  : MkIslemIDYeniKararDTO
            IDSucTipiGuncellemeKararDetay suctipiGuncellemeDTO = IDSucTipiGuncellemeKararDetay.builder()
                    .sucTipiGuncellemeListesi(sucTipiGuncellemeListesi)
                    .build();

            mahkemeKararIslem.setIDSucTipiGuncellemeKararDetay(suctipiGuncellemeDTO);
            return new Response<>(ResultCode.SUCCESS);

        } catch (Exception ex) {
            log.error("SucTipiGuncellemeKararEnricher failed. mahkemeKararIslemId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }

    }

    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararIslem detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }
}


