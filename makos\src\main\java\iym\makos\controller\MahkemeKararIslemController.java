package iym.makos.controller;

import iym.common.model.api.Response;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.MahkemeKararTalepBilgisiRequest;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mktalep.query.IDMahkemeKararTalepSorgulamaRequest;
import iym.makos.model.dto.mktalep.query.IDMahkemeKararTalepSorgulamaResponse;
import iym.makos.model.dto.mktalep.query.MahkemeKararTalepQueryResponse;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepSorguView;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.service.mkislem.MahkemeKararIslemService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/mahkemeKararIslem")
@Slf4j
public class MahkemeKararIslemController {

    @Autowired
    private MahkemeKararIslemService mahkemeKararIslemService;

    @PostMapping("/mahkemeKararIslemBilgisi")
    public ResponseEntity<MahkemeKararTalepQueryResponse> mahkemeKararTalepBilgisi(
            @Valid @RequestBody MahkemeKararTalepBilgisiRequest sorguParam, Authentication authentication) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("MahkemeKararIslem Detay Bilgi Sorgusu Received:{}, user:{}", sorguParam, user.getUsername());

            Response<MahkemeKarariInfo> mahkemeKarariInfoResp =  mahkemeKararIslemService.getMahkemeKararTalepDetails(sorguParam.getMahkemeKararId(), true);
            log.info("MahkemeKararIslem Detay Bilgi Sorgusu Result:{}", mahkemeKarariInfoResp);
            if(mahkemeKarariInfoResp.isSuccess()){
                return ResponseEntity.ok(MahkemeKararTalepQueryResponse.builder()
                        .iDMahkemeKarariInfo(mahkemeKarariInfoResp.getResult())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.SUCCESS)
                                .build())
                        .build());
            }
            else{
                return ResponseEntity.ok(MahkemeKararTalepQueryResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.FAILED)
                                .responseMessage("MahkemeKararIslem Detay Bilgi Sorgusu Başarısız: " )
                                .build())
                        .build());
            }


        } catch (Exception ex) {
            log.error("MahkemeKararIslem Detay Bilgi Sorgusu failed, requestId:{}", sorguParam.hashCode(), ex);
            MahkemeKararTalepQueryResponse response = MahkemeKararTalepQueryResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("MahkemeKararIslem Detay Bilgi Sorgusu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }



    @PostMapping("/mahkemeKararTalepIslemSorguID")
    public ResponseEntity<IDMahkemeKararTalepSorgulamaResponse> mahkemeKararTalepIslemSorgu(
            @Valid @RequestBody IDMahkemeKararTalepSorgulamaRequest sorguParam, Authentication authentication) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            List<MahkemeKararTalepSorguView> sonucListesi = null;//TODO : mkTalepIslemService.getMahkemeIslemDetailsByKararId(user, sorguParam);

            return ResponseEntity.ok(IDMahkemeKararTalepSorgulamaResponse.builder()
                    .mahkemeKararTalepSorguViewListesi(sonucListesi)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("mahkemeKararTalepSorgu process failed, requestId:{}", sorguParam.hashCode(), ex);
            IDMahkemeKararTalepSorgulamaResponse response = IDMahkemeKararTalepSorgulamaResponse.builder()
                    .mahkemeKararTalepSorguViewListesi(null)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Sorgu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }
}