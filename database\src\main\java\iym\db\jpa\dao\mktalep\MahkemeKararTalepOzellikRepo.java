package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.MahkemeKararTalepOzellik;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for MahkemeKararTalepOzellik entity
 */
@Repository
public interface MahkemeKararTalepOzellikRepo extends JpaRepository<MahkemeKararTalepOzellik, Long> {

    Optional<MahkemeKararTalepOzellik> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

}
