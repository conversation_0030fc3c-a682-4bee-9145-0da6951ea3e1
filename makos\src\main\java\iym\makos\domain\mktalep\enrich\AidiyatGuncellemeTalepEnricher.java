package iym.makos.domain.mktalep.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeAidiyatDetayTalepService;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.IDAidiyatGuncellemeKararDetay;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.MahkemeAidiyatDetayDTO;
import iym.makos.model.dto.view.info.MahkemeKararAidiyatGuncellemeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class AidiyatGuncellemeTalepEnricher implements MKTalepEnricher {

    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararService;
    private final DbMahkemeAidiyatDetayTalepService dbMahkemeAidiyatDetayService;

    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;


    @Autowired
    public AidiyatGuncellemeTalepEnricher(DbDetayMahkemeKararTalepService dbDetayMahkemeKararService
            , DbMahkemeAidiyatDetayTalepService dbMahkemeAidiyatDetayService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbMahkemeAidiyatDetayService = dbMahkemeAidiyatDetayService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }


    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {

        try {

            Long mahkemeKararTalepId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararTalepId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararTalepId yok");
            }

            List<MahkemeKararAidiyatGuncellemeInfo> aidiyatGuncellemeListesi = new ArrayList<>();

            List<DetayMahkemeKararTalep> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararTalepId(mahkemeKararTalepId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKararTalep -> {

                DetayMahkemeKararInfo detayMahkemeKararInfo = getMahkemeKararDetayInfo(detayMahkemeKararTalep);

                List<MahkemeAidiyatDetayTalep> list = dbMahkemeAidiyatDetayService.findByMahkemeKararDetayTalepId(detayMahkemeKararTalep.getId());
                List<MahkemeAidiyatDetayDTO> dtoList = getMahkemeAidiyatDetayDTOList(list);

                MahkemeKararAidiyatGuncellemeInfo mahkemeKararAidiyatGuncellemeInfo = MahkemeKararAidiyatGuncellemeInfo.builder()
                        .detayMahkemeKararInfo(detayMahkemeKararInfo)
                        .aidiyatGuncellemeListesi(dtoList)
                        .build();

                aidiyatGuncellemeListesi.add(mahkemeKararAidiyatGuncellemeInfo);

            });

            IDAidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay = IDAidiyatGuncellemeKararDetay.builder()
                    .aidiyatGuncellemeListesi(aidiyatGuncellemeListesi)
                    .build();

            mahkemeKararIslem.setIDAidiyatGuncellemeKararDetay(aidiyatGuncellemeKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("AidiyatGuncellemeTalepEnricher failed. mahkemeKararTalepId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }


    public List<MahkemeAidiyatDetayDTO> getMahkemeAidiyatDetayDTOList(List<MahkemeAidiyatDetayTalep> list) {
        List<MahkemeAidiyatDetayDTO> result = new ArrayList<>();

        CommonUtils.safeList(list).forEach(mahkemeAidiyatDetay -> {
            MahkemeAidiyatDetayDTO dd = MahkemeAidiyatDetayDTO.builder()
                    .detayMahkemeKararId(mahkemeAidiyatDetay.getMahkemeKararDetayTalepId())
                    .id(mahkemeAidiyatDetay.getId())
                    .iliskiliMahkemeKararId(mahkemeAidiyatDetay.getIliskiliMahkemeKararId())
                    .tarih(mahkemeAidiyatDetay.getTarih())
                    .durum(mahkemeAidiyatDetay.getDurum())
                    .mahkemeAidiyatKoduCikar(mahkemeAidiyatDetay.getMahkemeAidiyatKoduCikar())
                    .mahkemeAidiyatKoduEkle(mahkemeAidiyatDetay.getMahkemeAidiyatKoduEkle())
                    .mahkemeKararId(mahkemeAidiyatDetay.getMahkemeKararTalepId())
                    .build();
            result.add(dd);
        });

        return result;
    }


    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararTalep detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

}



