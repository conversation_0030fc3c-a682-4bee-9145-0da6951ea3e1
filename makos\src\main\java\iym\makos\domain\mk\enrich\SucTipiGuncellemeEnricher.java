package iym.makos.domain.mk.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.mk.DetayMahkemeKarar;
import iym.common.model.entity.iym.mk.MahkemeSucTipiDetay;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mk.DbDetayMahkemeKararService;
import iym.common.service.db.mk.DbHedeflerDetayService;
import iym.common.service.db.mk.DbMahkemeSucTipiDetayService;
import iym.common.util.CommonUtils;
import iym.makos.mapper.HedeflerInfoMapper;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDSucTipiGuncellemeKararDetay;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.IDKararSucTipiGuncellemeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class SucTipiGuncellemeEnricher implements MKEnricher {

    private final HedeflerInfoMapper hedeflerInfoMapper;
    private final DbHedeflerDetayService dbHedeflerDetayService;
    private final DbDetayMahkemeKararService dbDetayMahkemeKararService;
    private final DbMahkemeSucTipiDetayService dbMahkemeSucTipiDetayService;
    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public SucTipiGuncellemeEnricher(HedeflerInfoMapper hedeflerInfoMapper
            , DbHedeflerDetayService dbHedeflerDetayService
            , DbDetayMahkemeKararService dbDetayMahkemeKararService
            , DbMahkemeSucTipiDetayService dbMahkemeSucTipiDetayService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.hedeflerInfoMapper = hedeflerInfoMapper;
        this.dbHedeflerDetayService = dbHedeflerDetayService;
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbMahkemeSucTipiDetayService = dbMahkemeSucTipiDetayService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }


    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKarar) {
        try {

            Long mahkemeKararId = mahkemeKarar.getMahkemeKararId();
            if (mahkemeKararId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararId yok");
            }

            List<IDKararSucTipiGuncellemeInfo> sucTipiGuncellemeListesi = new ArrayList<>();

            List<DetayMahkemeKarar> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararId(mahkemeKararId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKarar -> {

                DetayMahkemeKararInfo detayMahkemeKararInfo = getMahkemeKararDetayInfo(detayMahkemeKarar);

                List<MahkemeSucTipiDetay> list = dbMahkemeSucTipiDetayService.findByMahkemeKararDetayId(detayMahkemeKarar.getId());

                IDKararSucTipiGuncellemeInfo iDKararSucTipiGuncellemeInfo = IDKararSucTipiGuncellemeInfo.builder()
                        .detayMahkemeKararInfo(detayMahkemeKararInfo)
                        //.guncellenecekKararBilgisi(mahkemeKararDetayInfo)
                        .build();

                sucTipiGuncellemeListesi.add(iDKararSucTipiGuncellemeInfo);

            });


            //Enrich edilecek nesne  : MkIslemIDYeniKararDTO
            IDSucTipiGuncellemeKararDetay suctipiGuncellemeDTO = IDSucTipiGuncellemeKararDetay.builder()
                    .sucTipiGuncellemeListesi(sucTipiGuncellemeListesi)
                    .build();

            mahkemeKarar.setIDSucTipiGuncellemeKararDetay(suctipiGuncellemeDTO);
            return new Response<>(ResultCode.SUCCESS);

        } catch (Exception ex) {
            log.error("SucTipiGuncellemeTalepEnricher failed. mahkemeKararId:{}", mahkemeKarar.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }

    }

    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKarar detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }
}


