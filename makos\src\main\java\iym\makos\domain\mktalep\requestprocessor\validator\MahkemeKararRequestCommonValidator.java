package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.model.entity.iym.EvrakGelenKurumlar;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.service.db.DbEvrakGelenKurumlarService;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.validation.ValidationResult;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class MahkemeKararRequestCommonValidator {

    private final DbIllerService dbIllerService;
    private final DbEvrakGelenKurumlarService dbEvrakGelenKurumlarService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;
    private final DbEvrakKayitService dbEvrakKayitService;

    @Autowired
    public MahkemeKararRequestCommonValidator(DbIllerService dbIllerService
            , DbEvrakGelenKurumlarService dbEvrakGelenKurumlarService
            , DbMahkemeBilgiService dbMahkemeBilgiService
            , DbEvrakKayitService dbEvrakKayitService) {
        this.dbIllerService = dbIllerService;
        this.dbEvrakGelenKurumlarService = dbEvrakGelenKurumlarService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
        this.dbEvrakKayitService = dbEvrakKayitService;
    }

    public ValidationResult validate(@NotNull MkTalepRequest request) {

        try {
            ValidationResult validationResult = request.isValid();

            if (!validationResult.isValid()) {
                return validationResult;
            }

            //EVRAK Kontrolleri
            String evrakGelenIlIlceKodu = request.getEvrakDetay().getGeldigiIlIlceKodu();
            Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(evrakGelenIlIlceKodu);
            if (ilIlceOpt.isEmpty()) {
                validationResult.addFailedReason(String.format("%s koduna sahip  evrak il/ilçe verisi sistemde bulunamadı", evrakGelenIlIlceKodu));
            }

            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            Optional<EvrakGelenKurumlar> evrakGelenKurumlarOpt = dbEvrakGelenKurumlarService.findByKurumKod(evrakGelenKurumKodu);
            if (evrakGelenKurumlarOpt.isEmpty()) {
                validationResult.addFailedReason(String.format("%s numaralı evrak kurum kodu sistemde bulunamadı", evrakGelenIlIlceKodu));
            }

            String evrakGelenEvrakNo = request.getEvrakDetay().getEvrakNo();
            List<EvrakKayit> ayniEvraklar = dbEvrakKayitService.findAllByEvrakNoAndEvrakGeldigiKurumKodu(evrakGelenEvrakNo, evrakGelenKurumKodu);
            if (!ayniEvraklar.isEmpty()) {
                validationResult.addFailedReason(String.format(MakosResponseErrorCodes.EVRAK_ZATEN_VAR, evrakGelenEvrakNo, evrakGelenKurumKodu));
            }

            //Mahkeme Karar Kontrolleri
            String mahkemeKodu = request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
            //Butun tiplerde mahkeme karar bulunmak zorunda oldugu icin mahkeme kodundan mahkeme bilgilerini kontrol et.
            Optional<MahkemeBilgi> mahkemeBilgiDb = dbMahkemeBilgiService.findByMahkemeKodu(mahkemeKodu);
            if (mahkemeBilgiDb.isEmpty()) {
                validationResult.addFailedReason(mahkemeKodu + " kodu ile kayıtlı bir mahkeme bilgisi bulunamamıştır.");
            }

            Optional<Iller> mahkemeilIlceDb = dbIllerService.findByIlIlceKodu(request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeIlIlceKodu());
            if (mahkemeilIlceDb.isEmpty()) {
                validationResult.addFailedReason(String.format("%s koduna sahip  mahkeme il/ilçe verisi sistemde bulunamadı", evrakGelenIlIlceKodu));
            }

            return validationResult;

        } catch (Exception ex) {
            log.error("Validation failed", ex);
            return new ValidationResult("Validation failed. Internal error");
        }
    }
}
