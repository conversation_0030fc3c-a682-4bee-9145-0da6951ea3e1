package iym.makos.domain.mktalep.requestprocessor.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDHedefGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.IDHedefGuncellemeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IDHedefGuncellemeRequestProcessor extends MakosRequestProcessorBase<IDHedefGuncellemeRequest, IDHedefGuncellemeResponse> {

    @Override
    public IDHedefGuncellemeResponse process(IDHedefGuncellemeRequest request, MakosUserDetails islemYapanKullanici) {

        MakosApiResponse preCheck = preCheck(request, islemYapanKullanici);
        log.error("IDHedefGuncelleme process failed: reason:{}", preCheck.getResponseMessage());
        if (preCheck.getResponseCode() != MakosResponseCode.SUCCESS){
            return IDHedefGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage(preCheck.getResponseMessage())
                            .build())
                    .build();
        }

        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {

                return IDHedefGuncellemeResponse.builder()
                        .requestId(request.getId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            MkTalepDbSaveRequest<IDHedefGuncellemeRequest> saveRequest = MkTalepDbSaveRequest.<IDHedefGuncellemeRequest>builder()
                    .kararRequest(request)
                    .kullaniciId(islemYapanKullanici.getUserId())
                    .build();
            MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = requestSaver.kaydet(saveRequest);

            return IDHedefGuncellemeResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepIdWithEvrakId.getEvrakId())
                    .mahkemeKararTalepId(mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId())
                    .build();

        } catch (MakosResponseException ex) {
            log.error("IDHedefAdSoyadGuncelleme process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDHedefGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        } catch (Exception ex) {
            log.error("IDHedefAdSoyadGuncelleme process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDHedefGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        }
    }
}
