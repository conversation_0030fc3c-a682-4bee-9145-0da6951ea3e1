package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.genelevrak.GenelEvrakRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Component
@Slf4j
public class GenelEvrakDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<GenelEvrakRequest> {

    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final KararRequestMapper kararRequestMapper;

    @Autowired
    public GenelEvrakDBSaveHandler(DbMahkemeKararTalepService dbMahkemeKararTalepService, KararRequestMapper kararRequestMapper) {
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.kararRequestMapper = kararRequestMapper;
    }

    @Override
    public void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<GenelEvrakRequest> request) {

    }
}

