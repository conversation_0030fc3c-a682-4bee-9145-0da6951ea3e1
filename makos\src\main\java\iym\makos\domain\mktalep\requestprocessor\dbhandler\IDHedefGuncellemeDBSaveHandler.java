package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.HedefGuncellemeAlan;
import iym.makos.model.api.HedefGuncellemeBilgi;
import iym.makos.model.api.HedefGuncellemeDetay;
import iym.makos.model.api.HedefGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.IDHedefGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDHedefGuncellemeRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IDHedefGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDHedefGuncellemeRequest> {
    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbHedeflerService dbHedeflerService;
    private final KararRequestMapper kararRequestMapper;
    private final DbHedeflerDetayTalepService dbHedeflerDetayTalepService;

    @Autowired
    public IDHedefGuncellemeDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService
            , DbHedeflerService dbHedeflerService
            , KararRequestMapper kararRequestMapper
            , DbHedeflerDetayTalepService dbHedeflerDetayTalepService
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbHedeflerService = dbHedeflerService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbHedeflerDetayTalepService = dbHedeflerDetayTalepService;
    }

    @Override
    public void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDHedefGuncellemeRequest> request) {

        validateRequest(request);

        IDHedefGuncellemeRequest kararRequest = request.getKararRequest();

        LocalDateTime saveDateTime = LocalDateTime.now();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();

        IDHedefGuncellemeRequestDetay hedefGuncellemeRequestDetay = kararRequest.getHedefGuncellemeRequestDetay();

        for (HedefGuncellemeKararDetay hedefBilgisi : hedefGuncellemeRequestDetay.getHedefGuncellemeKararDetayListesi()) {

            //Güncellemeye konu mahkeme karari bul
            MahkemeKararDetay iliskiliMahkemeKararDetayRequest = hedefBilgisi.getMahkemeKararDetay();
            Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                    , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                    , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                    , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
            if (iliskiliMahkemeKararOpt.isEmpty()) {
                String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                throw new MakosResponseException(errorStr);
            }
            MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();

            //Guncellemeye konu mahkeme karar bilgilerini detay olarak kaydet.
            DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, request.getKullaniciId(), saveDateTime);
            DetayMahkemeKararTalep savedDetayMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

            //Her bir hedefin iliskili mahkeme kararda olup olmadigini kontrol et
            List<HedefGuncellemeDetay> hedefListesi = hedefBilgisi.getHedefGuncellemeDetayListesi();
            if (hedefListesi != null && !hedefListesi.isEmpty()) {
                for (HedefGuncellemeDetay hedefGuncellemeDetay : hedefListesi) {

                    String hedefNo = hedefGuncellemeDetay.getHedef().getHedefNo();
                    HedefTip hedefTipi = hedefGuncellemeDetay.getHedef().getHedefTip();

                    Optional<Hedefler> iliskiliHedef = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, hedefTipi.getHedefKodu());
                    if (iliskiliHedef.isEmpty()) {
                        throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                    }

                    HedeflerDetayTalep hedeflerDetayTalep = new HedeflerDetayTalep();
                    for (HedefGuncellemeBilgi hedefGuncellemeBilgisi : hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi()) {
                        if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.AD) {
                            hedeflerDetayTalep.setHedefAdi(hedefGuncellemeBilgisi.getYeniDegeri());
                        } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.SOYAD) {
                            hedeflerDetayTalep.setHedefSoyadi(hedefGuncellemeBilgisi.getYeniDegeri());
                        } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.TCKIMlIKNO) {
                            hedeflerDetayTalep.setTcKimlikNo(hedefGuncellemeBilgisi.getYeniDegeri());
                        } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.CANAK_NO) {
                            hedeflerDetayTalep.setCanakNo(hedefGuncellemeBilgisi.getYeniDegeri());
                        }
                    }

                    String updateColumnNames = hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi().stream()
                            .map(guncellemeBilgi -> guncellemeBilgi.getHedefGuncellemeAlan().name())
                            .collect(Collectors.joining(","));

                    hedeflerDetayTalep.setUpdateColumnNames(updateColumnNames);
                    //her bir hedefi guncellenecek mahkeme karar ile iliskilendir.
                    hedeflerDetayTalep.setDetayMahkemeKararTalepId(savedDetayMahkemeKararTalep.getId());
                    hedeflerDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);

                    HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);
                    if (savedHedeflerDetayTalep == null) {
                        throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_HEDEFDETAY_KAYDETMEHATASI);
                    }
                }
            }

        }
    }
}

