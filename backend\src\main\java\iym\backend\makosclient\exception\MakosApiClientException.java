package iym.backend.makosclient.exception;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.util.JsonUtils;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * Custom exception for MAKOS API client that provides access to HTTP status code and response body separately
 */
@Getter
public class MakosApiClientException extends RuntimeException {

    private static final ObjectMapper objectMapper = JsonUtils.getMapper();
    /**
     * -- GETTER --
     * Get the HTTP status code
     */
    private final HttpStatus httpStatus;
    /**
     * -- GETTER --
     * Get the response body as JSON string
     */
    private final String responseBody;

    public MakosApiClientException(HttpStatus httpStatus, String responseBody) {
        super(extractMessageFromJson(responseBody));
        this.httpStatus = httpStatus;
        this.responseBody = responseBody;
    }

    public MakosApiClientException(HttpStatus httpStatus, String responseBody, Throwable cause) {
        super(extractMessageFromJson(responseBody), cause);
        this.httpStatus = httpStatus;
        this.responseBody = responseBody;
    }

    /**
     * Extract error message from JSON response body if it contains responseMessage attribute or ProblemDetail format
     *
     * @param responseBody JSON response body
     * @return extracted message or original response body if parsing fails
     */
    private static String extractMessageFromJson(String responseBody) {
        if (responseBody == null || responseBody.trim().isEmpty()) {
            return responseBody;
        }

        try {

            JsonNode jsonNode = objectMapper.readTree(responseBody);

            // Handle ProblemDetail format (RFC 7807)
            JsonNode detailNode = jsonNode.get("detail");
            if (detailNode != null && !detailNode.isNull()) {
                return detailNode.asText();
            }

            // Handle FailedApiResponse instance
            // Try to return responseMessage text if exists
            JsonNode responseMessageNode = jsonNode.get("responseMessage");
            if (responseMessageNode != null && !responseMessageNode.isNull()) {
                return responseMessageNode.asText();
            }

            // Handle Response<T> instances
            // Try to get response object first, then responseMessage from it, if exists
            JsonNode responseNode = jsonNode.get("response");
            if (responseNode != null && !responseNode.isNull()) {
                responseMessageNode = responseNode.get("responseMessage");
                if (responseMessageNode != null && !responseMessageNode.isNull()) {
                    return responseMessageNode.asText();
                }
            }

            // Fallback to original response body
            return responseBody;

        } catch (Exception e) {
            // If JSON parsing fails, return original response body
            return responseBody;
        }
    }
}