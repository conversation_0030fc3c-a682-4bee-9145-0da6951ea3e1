package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;


public interface MahkemeKararDBSaveHandler<T extends MkTalepRequest> {

    MahkemeKararTalepIdWithEvrakId kaydet(MkTalepDbSaveRequest<T> request);

    Long saveEvrakBilgileri(MkTalepDbSaveRequest<T> request);

    Long saveMahkemeKararTalep(Long evrakId, MkTalepDbSaveRequest<T> request);

    void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<T> request);

    Class<T> getRelatedRequestType();
}

