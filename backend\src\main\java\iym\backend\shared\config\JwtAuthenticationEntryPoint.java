package iym.backend.shared.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.util.JsonUtils;
import iym.common.model.api.ProblemDetailBuilder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ProblemDetail;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * JWT Authentication Entry Point for IYM Backend
 * Handles unauthorized access attempts
 */
@Component
@Slf4j
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {

        log.error("Unauthorized error: {}", authException.getMessage());

        response.setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        ProblemDetail problemDetail = ProblemDetailBuilder.authenticationError(authException.getMessage());
        problemDetail.setProperty("path", request.getServletPath());

        final ObjectMapper mapper = JsonUtils.getMapper();
        mapper.writeValue(response.getOutputStream(), problemDetail);
    }
}

