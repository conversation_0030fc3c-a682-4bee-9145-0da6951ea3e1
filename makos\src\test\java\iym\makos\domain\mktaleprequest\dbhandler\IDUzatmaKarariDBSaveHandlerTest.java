package iym.makos.domain.mktaleprequest.dbhandler;


import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.IDUzatmaKarariDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararRequestCommonDbSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThatThrownBy;


@ExtendWith(MockitoExtension.class)
@DisplayName("IDUzatmaKarariDBSaveHandlerTest Unit Tests")
public class IDUzatmaKarariDBSaveHandlerTest extends BaseDomainUnitTest{

    @InjectMocks
    private IDUzatmaKarariDBSaveHandler handler;

    @Mock
    private DbMahkemeKararService dbMahkemeKararService;
    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;
    @Mock
    private DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    @Mock
    private DbHedeflerTalepService dbHedeflerTalepService;
    @Mock
    private DbHedeflerDetayTalepService dbHedeflerDetayTalepService;
    @Mock
    private DbHedeflerService dbHedeflerService;
    @Mock
    private KararRequestMapper kararRequestMapper;
    @Mock
    private MahkemeKararRequestCommonDbSaveHandler mahkemeKararRequestDbSaveHandlerHelper;


    private MahkemeKararTalepIdWithEvrakId talepIdWithEvrakId;
    private IDUzatmaKarariRequest request;
    private MkTalepDbSaveRequest<IDUzatmaKarariRequest> dbSaveRequest;
    private LocalDateTime kayitTarihi;
    private Long kullaniciId;

    @InjectMocks
    private IDUzatmaKarariDBSaveHandler dbSaveHandler;


    @BeforeEach
    void setUp() {

        talepIdWithEvrakId = new MahkemeKararTalepIdWithEvrakId(1L, 2L);
        request = new IDUzatmaKarariRequest();

        kayitTarihi = LocalDateTime.now();
        kullaniciId = 42L;


        talepIdWithEvrakId = createTestMahkemeKararTalepIdWithEvrakId();

        dbSaveRequest = MkTalepDbSaveRequest.<IDUzatmaKarariRequest>builder()
                .kararRequest(request)
                .kullaniciId(kullaniciId)
                .build();
    }


    @Test
    void advanceHandleDbSave_nullTalep_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(null, dbSaveRequest))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("mahkemeKarar/EvrakId boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullRequest_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, null))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("request boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullKullaniciId_throwsException() {
        MkTalepDbSaveRequest<IDUzatmaKarariRequest> dbSaveRequest = MkTalepDbSaveRequest.<IDUzatmaKarariRequest>builder()
                .kararRequest(request)
                .kullaniciId(null)
                .build();
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, dbSaveRequest))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kullaniciId boş olamaz");
    }



}
