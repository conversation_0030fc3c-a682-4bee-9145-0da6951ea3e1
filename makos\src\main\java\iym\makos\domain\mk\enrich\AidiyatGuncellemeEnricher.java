package iym.makos.domain.mk.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.mk.DetayMahkemeKarar;
import iym.common.model.entity.iym.mk.MahkemeAidiyatDetay;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mk.DbDetayMahkemeKararService;
import iym.common.service.db.mk.DbMahkemeAidiyatDetayService;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.IDAidiyatGuncellemeKararDetay;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.info.DetayMahkemeKararInfo;
import iym.makos.model.dto.view.info.MahkemeAidiyatDetayDTO;
import iym.makos.model.dto.view.info.MahkemeKararAidiyatGuncellemeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class AidiyatGuncellemeEnricher implements MKEnricher {

    private final DbDetayMahkemeKararService dbDetayMahkemeKararService;
    private final DbMahkemeAidiyatDetayService dbMahkemeAidiyatDetayService;

    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;


    @Autowired
    public AidiyatGuncellemeEnricher(DbDetayMahkemeKararService dbDetayMahkemeKararService
            , DbMahkemeAidiyatDetayService dbMahkemeAidiyatDetayService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService
    ) {
        this.dbDetayMahkemeKararService = dbDetayMahkemeKararService;
        this.dbMahkemeAidiyatDetayService = dbMahkemeAidiyatDetayService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }


    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKarar) {

        try {

            Long mahkemeKararId = mahkemeKarar.getMahkemeKararId();
            if (mahkemeKararId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararId yok");
            }

            List<MahkemeKararAidiyatGuncellemeInfo> aidiyatGuncellemeListesi = new ArrayList<>();

            List<DetayMahkemeKarar> detayMahkemeKararList = dbDetayMahkemeKararService.findByMahkemeKararId(mahkemeKararId);
            CommonUtils.safeList(detayMahkemeKararList).forEach(detayMahkemeKarar -> {

                DetayMahkemeKararInfo detayMahkemeKararInfo = getMahkemeKararDetayInfo(detayMahkemeKarar);

                List<MahkemeAidiyatDetay> list = dbMahkemeAidiyatDetayService.findByMahkemeKararDetayId(detayMahkemeKarar.getId());
                List<MahkemeAidiyatDetayDTO> dtoList = getMahkemeAidiyatDetayDTOList(list);

                MahkemeKararAidiyatGuncellemeInfo mahkemeKararAidiyatGuncellemeInfo = MahkemeKararAidiyatGuncellemeInfo.builder()
                        .detayMahkemeKararInfo(detayMahkemeKararInfo)
                        .aidiyatGuncellemeListesi(dtoList)
                        .build();

                aidiyatGuncellemeListesi.add(mahkemeKararAidiyatGuncellemeInfo);

            });

            IDAidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay = IDAidiyatGuncellemeKararDetay.builder()
                    .aidiyatGuncellemeListesi(aidiyatGuncellemeListesi)
                    .build();

            mahkemeKarar.setIDAidiyatGuncellemeKararDetay(aidiyatGuncellemeKararDetay);
            return new Response<>(ResultCode.SUCCESS);

        } catch (Exception ex) {
            log.error("AidiyatGuncellemeEnricher failed. mahkemeKararId:{}", mahkemeKarar.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }


    public List<MahkemeAidiyatDetayDTO> getMahkemeAidiyatDetayDTOList(List<MahkemeAidiyatDetay> list) {
        List<MahkemeAidiyatDetayDTO> result = new ArrayList<>();

        CommonUtils.safeList(list).forEach(mahkemeAidiyatDetay -> {
            MahkemeAidiyatDetayDTO dd = MahkemeAidiyatDetayDTO.builder()
                    .detayMahkemeKararId(mahkemeAidiyatDetay.getMahkemeKararDetayId())
                    .id(mahkemeAidiyatDetay.getId())
                    .iliskiliMahkemeKararId(mahkemeAidiyatDetay.getIliskiliMahkemeKararId())
                    .tarih(mahkemeAidiyatDetay.getTarih())
                    .durum(mahkemeAidiyatDetay.getDurum())
                    .mahkemeAidiyatKoduCikar(mahkemeAidiyatDetay.getMahkemeAidiyatKoduCikar())
                    .mahkemeAidiyatKoduEkle(mahkemeAidiyatDetay.getMahkemeAidiyatKoduEkle())
                    .mahkemeKararId(mahkemeAidiyatDetay.getMahkemeKararId())
                    .build();
            result.add(dd);
        });

        return result;
    }


    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKarar detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

}



