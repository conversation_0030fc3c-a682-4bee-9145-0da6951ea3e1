package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.GuncellemeTip;
import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeAidiyatDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDAidiyatBilgisiGuncellemeRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDAidiyatBilgisiGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDAidiyatBilgisiGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final KararRequestMapper kararRequestMapper;
    private final DbMahkemeAidiyatDetayTalepService dbMahkemeAidiyatDetayTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService;

    @Autowired
    public IDAidiyatBilgisiGuncellemeDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , KararRequestMapper kararRequestMapper
            , DbMahkemeAidiyatDetayTalepService dbMahkemeAidiyatDetayTalepService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService
            , DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbMahkemeAidiyatDetayTalepService = dbMahkemeAidiyatDetayTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbMahkemeKararAidiyatService = dbMahkemeKararAidiyatService;
    }

    @Override
    public void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDAidiyatBilgisiGuncellemeRequest> request) {

        validateRequest(request);

        LocalDateTime saveDateTime = LocalDateTime.now();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();

        IDAidiyatBilgisiGuncellemeRequestDetay guncellemeRequestDetay = request.getKararRequest().getAidiyatBilgisiGuncellemeRequestDetay();
        for (AidiyatGuncellemeKararDetay guncellemeBilgisi : guncellemeRequestDetay.getAidiyatGuncellemeKararDetayListesi()) {
            //Güncellemeye konu mahkeme karari bul
            MahkemeKararDetay iliskiliMahkemeKararDetayRequest = guncellemeBilgisi.getMahkemeKararDetay();
            Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                    , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                    , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                    , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
            if (iliskiliMahkemeKararOpt.isEmpty()) {
                String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                throw new MakosResponseException(errorStr);
            }
            MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();


            DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, request.getKullaniciId(), saveDateTime);
            DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

            List<AidiyatGuncellemeDetay> aidiyatGuncellemeListesi = guncellemeBilgisi.getAidiyatGuncellemeDetayListesi();
            for (AidiyatGuncellemeDetay aidiyatGuncellemeDetay : aidiyatGuncellemeListesi) {

                GuncellemeTip guncellemeTip = aidiyatGuncellemeDetay.getGuncellemeTip();
                String aidiyatKodu = aidiyatGuncellemeDetay.getAidiyatKodu();

                Optional<MahkemeAidiyat> mahkemeAidiyat = dbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(iliskiliMahkemeKarar.getId(), aidiyatKodu);
                if (guncellemeTip == GuncellemeTip.EKLE && mahkemeAidiyat.isPresent()) {
                    throw new MakosResponseException("Zaten var");
                } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeAidiyat.isEmpty()) {
                    throw new MakosResponseException("Böyle bir aidiyat zaten yok");
                }

                MahkemeAidiyatDetayTalep detayTalep = new MahkemeAidiyatDetayTalep();
                detayTalep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                //DetayMahkemeKararTalep'de olmasina ragmen burada yine de kaydediliyor.
                detayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
                detayTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                detayTalep.setTarih(saveDateTime);
                if (guncellemeTip == GuncellemeTip.EKLE) {
                    detayTalep.setMahkemeAidiyatKoduEkle(aidiyatGuncellemeDetay.getAidiyatKodu());
                } else {
                    detayTalep.setMahkemeAidiyatKoduCikar(aidiyatGuncellemeDetay.getAidiyatKodu());
                }
                MahkemeAidiyatDetayTalep savedMahkemeAidiyatDetayTalep = dbMahkemeAidiyatDetayTalepService.save(detayTalep);
            }
        }
    }
}

