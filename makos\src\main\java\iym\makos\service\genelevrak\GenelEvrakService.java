package iym.makos.service.genelevrak;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguInfo;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalepOzellik;
import iym.common.model.exception.MissingDbRecordException;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.mktalep.DbHtsMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepOzellikService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.enrich.service.MkTalepEnricherService;
import iym.makos.domain.mktalep.enrich.service.MkTalepKararTuruLookupService;
import iym.makos.mapper.MahkemeKararTalepMapper;
import iym.makos.mapper.MahkemeKararTalepSorguMapper;
import iym.makos.model.dto.db.MahkemeKararTalepDTO;
import iym.makos.model.dto.mktalep.query.IDMahkemeKararTalepSorgulamaRequest;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepSorguView;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.info.EvrakDetayInfo;
import iym.makos.model.dto.view.info.MahkemeKararBilgisiInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for MahkemeKararTalep operations
 */
@Service
@Slf4j
public class GenelEvrakService {


    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;
    private final DbMahkemeKararTalepOzellikService dbMahkemeKararTalepOzellikService;
    private final MkTalepEnricherService mkTalepEnricherService;
    private final DbEvrakKayitService dbEvrakKayitService;
    private final MkTalepKararTuruLookupService mkTalepKararTuruLookupService;
    private final MahkemeKararTalepMapper mahkemeKararTalepMapper;
    private final MahkemeKararTalepSorguMapper mahkemeKararTalepSorguMapper;


    @Autowired
    public GenelEvrakService(DbMahkemeKararTalepService dbMahkemeKararTalepService
            , MahkemeKararTalepMapper mahkemeKararTalepMapper
            , MahkemeKararTalepSorguMapper mahkemeKararTalepSorguMapper
            , MkTalepEnricherService mkTalepEnricherService
            , DbEvrakKayitService dbEvrakKayitService
            , MkTalepKararTuruLookupService mkTalepKararTuruLookupService
            , DbMahkemeKararTalepOzellikService dbMahkemeKararTalepOzellikService
            , DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService

    ) {
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.mahkemeKararTalepMapper = mahkemeKararTalepMapper;
        this.mahkemeKararTalepSorguMapper = mahkemeKararTalepSorguMapper;
        this.mkTalepEnricherService = mkTalepEnricherService;
        this.dbEvrakKayitService = dbEvrakKayitService;
        this.mkTalepKararTuruLookupService = mkTalepKararTuruLookupService;
        this.dbMahkemeKararTalepOzellikService = dbMahkemeKararTalepOzellikService;
        this.dbHtsMahkemeKararTalepService = dbHtsMahkemeKararTalepService;
    }





    /**
     * Get paginated mahkeme karar talep records
     *
     * @param pageable Pageable
     * @return Page of MahkemeKararTalepDTO
     */

    public Page<MahkemeKararTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeKararTalep> mahkemeKararTalepPage = dbMahkemeKararTalepService.findAll(pageable);
        List<MahkemeKararTalepDTO> dtoList = mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeKararTalepPage.getTotalElements());
    }

    /**
     * Create new mahkeme karar talep
     *
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Created MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO create(MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        // Check if mahkeme karar already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
                mahkemeKararTalepDTO.getMahkemeAdi() != null &&
                mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null) {
            // TODO
        }

        MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepMapper.toEntity(mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.save(mahkemeKararTalep);
        log.info("Mahkeme karar talep oluşturuldu: {}", mahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(mahkemeKararTalep);
    }

    /**
     * Update mahkeme karar talep
     *
     * @param id                   Mahkeme karar talep ID
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Updated MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO update(Long id, MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        MahkemeKararTalep existingMahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new MissingDbRecordException("Mahkeme karar talep bulunamadı: " + id));

        // Check if mahkeme karar is being changed and already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
                mahkemeKararTalepDTO.getMahkemeAdi() != null &&
                mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null &&
                (!mahkemeKararTalepDTO.getMahkemeKararNo().equals(existingMahkemeKararTalep.getMahkemeKararNo()) ||
                        !mahkemeKararTalepDTO.getMahkemeAdi().equals(existingMahkemeKararTalep.getMahkemeAdi()) ||
                        !mahkemeKararTalepDTO.getMahkemeIlIlceKodu().equals(existingMahkemeKararTalep.getMahkemeIlIlceKodu()))) {

            // TODO
        }

        MahkemeKararTalep updatedMahkemeKararTalep = mahkemeKararTalepMapper.updateEntityFromDto(existingMahkemeKararTalep, mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.update(updatedMahkemeKararTalep);
        log.info("Mahkeme karar talep güncellendi: {}", updatedMahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(updatedMahkemeKararTalep);
    }

    /**
     * Delete mahkeme karar talep
     *
     * @param id Mahkeme karar talep ID
     */

    public void delete(Long id) {
        MahkemeKararTalep mahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new MissingDbRecordException("Mahkeme karar talep bulunamadı: " + id));

        dbMahkemeKararTalepService.delete(mahkemeKararTalep);
        log.info("Mahkeme karar talep silindi: {}", id);
    }


    public Response<MahkemeKarariInfo> getMahkemeKararTalepDetails(long mahkemeKararTalepId, boolean enrich) {

        log.debug("getMahkemeKararTalepDetails for mahkeme karar islem: {}", mahkemeKararTalepId);

        try {

            Response<MahkemeKarariInfo> response = getMahkemeKararTalep(mahkemeKararTalepId);
            if (!response.isSuccess()) {
                return new Response<>(ResultCode.FAILED, String.format("getMahkemeKararTalepDetails hatası for  mahkemeKararTalepId: %d Hata: %s ", mahkemeKararTalepId, response.getResultDetails()));
            }

            MahkemeKarariInfo iDMahkemeKarariInfo = response.getResult();

            if (enrich) {
                mkTalepEnricherService.enrich(iDMahkemeKarariInfo);
            }

            log.debug("getMahkemeIslemDetailsByKararId  mahkemeKararIslem: {}", mahkemeKararTalepId);
            return response;


        } catch (Exception ex) {
            log.error("getMahkemeIslemDetailsByKararId hatası : ", ex);
            return new Response<>(ResultCode.FAILED, String.format("getMahkemeIslemDetailsByKararId hatası for  mahkemeKararIslemId: %d ", mahkemeKararTalepId));
        }

    }

    private Response<MahkemeKarariInfo> getMahkemeKararTalep(long mahkemeKararTalepId) {
        MahkemeKarariInfo result = new MahkemeKarariInfo();

        //TODO: Testi duzenle
        Optional<MahkemeKararTalepOzellik> mahkemeKararTalepOzellikOpt = dbMahkemeKararTalepOzellikService.findByMahkemeKararTalepId(mahkemeKararTalepId);
        if (mahkemeKararTalepOzellikOpt.isEmpty()) {
            return new Response<>(ResultCode.FAILED, String.format("Mahkeme Karar Talep Özellik Bulunamadı. Mahkeme Karar Talep Id %s", mahkemeKararTalepId));
        }

        KararTuru kararTuru = CommonUtils.getKararTuruFromName(mahkemeKararTalepOzellikOpt.get().getKararTuru());
        if (kararTuru == null) {
            return new Response<>(ResultCode.FAILED, String.format("Mahkeme Karar Talep için karar türü bulunamadı. mahkemeKararTalepId : %s", mahkemeKararTalepId));
        }

        result.setRequestId(mahkemeKararTalepOzellikOpt.get().getRequestId());
        result.setKararTuru(kararTuru);

        Long evrakId = null;

        if (kararTuru == KararTuru.ILETISIMIN_TESPITI) {

            Optional<HtsMahkemeKararTalep> htsMahkemeKararTalepOpt = dbHtsMahkemeKararTalepService.findById(mahkemeKararTalepId);
            if (htsMahkemeKararTalepOpt.isEmpty()) {
                return new Response<>(ResultCode.FAILED, String.format("HTS Mahkeme Karar Talep Bulunamadı. HTS Mahkeme Karar Talep Id %s", mahkemeKararTalepId));
            }

            HtsMahkemeKararTalep htsMahkemeKararTalep = htsMahkemeKararTalepOpt.get();
            result.setMahkemeKararId(htsMahkemeKararTalep.getId());
            result.setDurumu(htsMahkemeKararTalep.getDurum());
            evrakId = htsMahkemeKararTalep.getEvrakId();




        } else {

            //info nun mahkeme karar bilgileri
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = dbMahkemeKararTalepService.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                return new Response<>(ResultCode.FAILED, String.format("Mahkeme Karar Talep Bulunamadı. Mahkeme Karar Talep Id %s", mahkemeKararTalepId));
            }

            MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepOpt.get();
            evrakId = mahkemeKararTalep.getEvrakId();
            result.setMahkemeKararId(mahkemeKararTalep.getId());
            result.setDurumu(mahkemeKararTalep.getDurum());

            MahkemeKararBilgisiInfo mahkemeKararBilgisiInfo = toMahkemeKararBilgisiInfo(mahkemeKararTalep);
            mahkemeKararBilgisiInfo.setKararTuru(kararTuru);
            /*
            MahkemeKararTuruResponse kararTuruResponse = mkTalepKararTuruLookupService.findKararTuru(mahkemeKararTalepId);
            if (kararTuruResponse.getResponse().getResponseCode().equals(MakosResponseCode.FAILED)) {
                return new Response<>(ResultCode.FAILED, String.format("Mahkeme Karar Talep için karar türü bulunamadı. mahkemeKararTalepId : %s", mahkemeKararTalepId));
            }*/

            // karar turu
            //result.setKararTuru(kararTuruResponse.getKararTuru());


            // mahkeme karar bilgisi
            result.setMahkemeKararBilgisi(mahkemeKararBilgisiInfo);

            // todo
            //result.setDosyaAdi();
        }

        //evrak bilgileri
        Optional<EvrakKayit> evrakKayitOpt = dbEvrakKayitService.findById(evrakId);
        if (evrakKayitOpt.isEmpty()) {
            return new Response<>(ResultCode.FAILED, String.format("Evrak Kayıt Bulunamadı. Evrak Id %s", evrakId));
        }

        EvrakKayit evrakKayit = evrakKayitOpt.get();

        result.setEvrakDetay(toEvrakDetayInfo(evrakKayit));


        return new Response<>(result);
    }

    public static MahkemeKararBilgisiInfo toMahkemeKararBilgisiInfo(MahkemeKararTalep mahkemeKararTalep) {
        return MahkemeKararBilgisiInfo.builder()
                .sorusturmaNo(mahkemeKararTalep.getSorusturmaNo())
                .mahkemeKararNo(mahkemeKararTalep.getMahkemeKararNo())
                .mahkemeKodu(mahkemeKararTalep.getMahkemeKodu())
                .mahkemeIlIlceKodu(mahkemeKararTalep.getMahkemeIlIlceKodu())
                .aciklama(mahkemeKararTalep.getAciklama())
                .kararBaslamaTarihi(mahkemeKararTalep.getMahKararBasTar())
                .kararBitisTarihi(mahkemeKararTalep.getMahKararBitisTar())
                .durumu(mahkemeKararTalep.getDurum())
                .build();
    }

    public static MahkemeKararBilgisiInfo toMahkemeKararBilgisiInfo(HtsMahkemeKararTalep mahkemeKararTalep) {
        return MahkemeKararBilgisiInfo.builder()
                .sorusturmaNo(mahkemeKararTalep.getSorusturmaNo())
                .mahkemeKararNo(mahkemeKararTalep.getMahkemeKararNo())
                .mahkemeKodu(mahkemeKararTalep.getMahkemeKodu())
                .mahkemeIlIlceKodu(mahkemeKararTalep.getMahkemeIli())
                .aciklama(mahkemeKararTalep.getAciklama())
                .durumu(mahkemeKararTalep.getDurum())
                .build();
    }

    public static EvrakDetayInfo toEvrakDetayInfo(EvrakKayit evrakKayit) {
        return EvrakDetayInfo.builder()
                .evrakNo(evrakKayit.getEvrakNo())
                .evrakKonusu(evrakKayit.getEvrakKonusu())
                .aciklama(evrakKayit.getAciklama())
                .acilmi(CommonUtils.safeString(evrakKayit.getAcilmi()).equals("E"))
                .evrakTarihi(evrakKayit.getEvrakTarihi())
                //.evrakTuru(KararTuru.GENEL_EVRAK)
                .durumu(evrakKayit.getDurumu())
                .evrakKurumKodu(evrakKayit.getEvrakGeldigiKurumKodu())
                .geldigiIlIlceKodu(evrakKayit.getGeldigiIlIlceKodu())
                .evrakKonusu(evrakKayit.getEvrakKonusu())
                .build();

    }

    /*
    public Response<MahkemeKararTalepViewDTO> getMahkemeKararTalepDetails(long mahkemeKararIslemId, boolean enrich) {

        log.debug("getMahkemeIslemDetailsByKararId for mahkeme karar islem: {}", mahkemeKararIslemId);

        try {

            Response<MahkemeKararTalepViewDTO> response = getMahkemeKararTalep(mahkemeKararIslemId);
            if (!response.isSuccess()){
                return new Response<>(ResultCode.FAILED, String.format("getMahkemeIslemDetailsByKararId hatası for  mahkemeKararIslemId: %d Hata: %s ", mahkemeKararIslemId, response.getResultDetails()));
            }

            MahkemeKararTalepViewDTO mahkemeKararTalepViewDTO = response.getResult();

            //enrich MahkemeKararIslemDTO related properties by kararturu
            if (enrich) {
                mkTalepEnricherService.enrich(mahkemeKararTalepViewDTO);
            }

            log.debug("getMahkemeIslemDetailsByKararId  mahkemeKararIslem: {}", mahkemeKararIslemId);
            return response;


        } catch (Exception ex) {
            return new Response<>(ResultCode.FAILED, String.format("getMahkemeIslemDetailsByKararId hatası for  mahkemeKararIslemId: %d ", mahkemeKararIslemId));
        }

    }

    private Response<MahkemeKararTalepViewDTO> getMahkemeKararTalep(long mahkemeKararIslemId) {

        MahkemeKararTalepViewDTO result = new MahkemeKararTalepViewDTO();

        //info nun mahkeme karar bilgileri
        Optional<MahkemeKararTalep> mahkemeKararTalepOpt = dbMahkemeKararTalepService.findById(mahkemeKararIslemId);
        if (mahkemeKararTalepOpt.isEmpty()) {
            return new Response<>(ResultCode.FAILED, String.format("Mahkeme Karar Talep Bulunamadı. Mahkeme Karar Talep Id %s", mahkemeKararIslemId));
        }

        MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepOpt.get();
        MahkemeKararBilgisiInfo mahkemeKararBilgisiInfo = toMahkemeKararBilgisiInfo(mahkemeKararTalep);

        MahkemeKararTuruResponse kararTuruResponse = mkTalepKararTuruLookupService.findKararTuru(mahkemeKararIslemId);
        if (kararTuruResponse.getResponse().getResponseCode().equals(MakosResponseCode.FAILED)) {
            return new Response<>(ResultCode.FAILED, String.format("Mahkeme Karar İşlem için karar türü bulunamadı. mahkemeKararIslemId : %s", mahkemeKararIslemId));
        }

        // karar turu
        result.setKararTuru(kararTuruResponse.getKararTuru());

        // mahkeme karar bilgisi
        result.setMahkemeKararBilgisi(mahkemeKararBilgisiInfo);

        // todo
        //result.setDosyaAdi();

        // durumu
        result.setDurumu(mahkemeKararTalep.getDurum());

        //evrak bilgileri
        Optional<EvrakKayit> evrakKayitOpt = dbEvrakKayitService.findById(mahkemeKararTalep.getEvrakId());
        if (evrakKayitOpt.isEmpty()) {
            return new Response<>(ResultCode.FAILED, String.format("Evrak Kayıt Bulunamadı. Evrak Id %s", mahkemeKararTalep.getEvrakId()));
        }

        EvrakKayit evrakKayit = evrakKayitOpt.get();

        result.setEvrakDetay(toEvrakDetayInfo(evrakKayit));

        return new Response<>(result);
    }

    public static MahkemeKararBilgisiInfo toMahkemeKararBilgisiInfo(MahkemeKararTalep mahkemeKararTalep) {
        return MahkemeKararBilgisiInfo.builder()
                .sorusturmaNo(mahkemeKararTalep.getSorusturmaNo())
                .mahkemeKararNo(mahkemeKararTalep.getMahkemeKararNo())
                .mahkemeKodu(mahkemeKararTalep.getMahkemeKodu())
                .mahkemeIlIlceKodu(mahkemeKararTalep.getMahkemeIlIlceKodu())
                .aciklama(mahkemeKararTalep.getAciklama())
                .kararBaslamaTarihi(mahkemeKararTalep.getMahKararBasTar())
                .kararBitisTarihi(mahkemeKararTalep.getMahKararBitisTar())
                .durumu(mahkemeKararTalep.getDurum())
                .build();
    }

    public static EvrakDetayInfo toEvrakDetayInfo(EvrakKayit evrakKayit) {
        return EvrakDetayInfo.builder()
                .evrakNo(evrakKayit.getEvrakNo())
                .evrakKonusu(evrakKayit.getEvrakKonusu())
                .aciklama(evrakKayit.getAciklama())
                .acilmi(CommonUtils.safeString(evrakKayit.getAcilmi()).equals("E"))
                .evrakTarihi(evrakKayit.getEvrakTarihi())
                //.evrakTuru(KararTuru.GENEL_EVRAK)
                .durumu(evrakKayit.getDurumu())
                .evrakKurumKodu(evrakKayit.getEvrakGeldigiKurumKodu())
                .geldigiIlIlceKodu(evrakKayit.getGeldigiIlIlceKodu())
                .evrakKonusu(evrakKayit.getEvrakKonusu())
                .build();

    }
    */

}
