-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;


-- Create MAHKEME_KARAR_TALEP_OZELLIK table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = '<PERSON><PERSON><PERSON><PERSON>_KARAR_TALEP_OZELLIK';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAH<PERSON><PERSON>_KARAR_TALEP_OZELLIK (
      MAH<PERSON>ME_KARAR_TALEP_ID NUMBER NOT NULL,
      <PERSON><PERSON><PERSON>_TURU VARCHAR2(50),
      REQUEST_ID RAW(16) DEFAULT SYS_GUID(),
      CONSTRAINT MK_TALEP_OZELLIK_ID_IDX PRIMARY KEY (MAHKEME_KARAR_TALEP_ID) ENABLE,
      CONSTRAINT FK_MK_TALEP_OZELLIK FOREIGN KEY (MAH<PERSON><PERSON>_KARAR_TALEP_ID)
              REFERENCES iym.<PERSON>HKEME_KARAR_TALEP(ID)
              ON DELETE CASCADE
    )';

  END IF;
END;
/

COMMIT;
