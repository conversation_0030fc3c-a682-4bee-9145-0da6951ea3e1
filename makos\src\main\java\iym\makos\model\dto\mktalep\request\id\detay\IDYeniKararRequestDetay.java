package iym.makos.model.dto.mktalep.request.id.detay;

import iym.common.validation.ValidationResult;
import iym.makos.model.api.IDHedefDetay;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class IDYeniKararRequestDetay implements IDRequestDetay {

    @NotNull
    @Size(min = 1)
    @Valid
    @Builder.Default
    private List<IDHedefDetay> hedefDetayListesi = new ArrayList<>();

    @Builder.Default
    private List<String> mahkemeAidiyatKodlari = new ArrayList<>();

    @Builder.Default
    private List<String> mahkemeSucTipiKodlari = new ArrayList<>();

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDYeniKararRequestDetay is valid");

        try {

            ValidationResult validationResult = new ValidationResult(true);

            if (hedefDetayListesi == null || hedefDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Yeni Kararda hedef detay listesi boş olamaz!");
                return validationResult;
            }

            for (IDHedefDetay IDHedefDetay : hedefDetayListesi) {
                if (IDHedefDetay.getIlgiliMahkemeKararDetayi() != null) {
                    validationResult.addFailedReason("Yeni Kararda ilişkili mahkeme karari dolu olamaz!");
                }

                if (IDHedefDetay.getUzatmaSayisi() != null && IDHedefDetay.getUzatmaSayisi() > 0) {
                    validationResult.addFailedReason("Yeni Kararda uzatma sayisi dolu olamaz!");
                }

                if (IDHedefDetay.getSure() == null) {
                    validationResult.addFailedReason("Hedefin süresi boş olamaz!");
                } else if (IDHedefDetay.getSure() <= 0) {
                    validationResult.addFailedReason("Hedefin süresi sıfırdan büyük olmalıdır.");
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

}

