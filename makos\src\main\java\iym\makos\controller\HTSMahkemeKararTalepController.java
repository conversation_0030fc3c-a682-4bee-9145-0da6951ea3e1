package iym.makos.controller;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.Response;
import iym.common.util.CommonUtils;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.mktalep.requestprocessor.service.MahkemeKararProcessService;
import iym.makos.domain.mktalep.stateupdater.service.MkTalepStateUpdaterService;
import iym.makos.model.MahkemeKararTalepBilgisiRequest;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.query.*;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.genelevrak.GenelEvrakSorgulamaRequest;
import iym.makos.model.dto.mktalep.request.genelevrak.GenelEvrakSorgulamaResponse;
import iym.makos.model.dto.mktalep.request.it.ITKararRequest;
import iym.makos.model.dto.mktalep.request.it.ITKararResponse;
import iym.makos.model.dto.mktalep.request.it.ITMahkemeKararTalepSorgulamaRequest;
import iym.makos.model.dto.mktalep.request.it.ITMahkemeKararTalepSorgulamaResponse;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepSorguView;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.service.file.FilePersisterService;
import iym.makos.service.mktalep.MahkemeKararTalepService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/htsMahkemeKararTalep")
@Slf4j
public class HTSMahkemeKararTalepController {

    public final static String MAHKEME_KARAR_FILE_PART = "mahkemeKararDosyasi";
    public final static String MAHKEME_KARAR_DETAY_JSON_PART = "mahkemeKararDetay";

    @Autowired
    private MahkemeKararTalepService mahkemeKararTalepService;

    @Autowired
    private MahkemeKararProcessService mahkemeKararProcessService;

    @Autowired
    private MkTalepStateUpdaterService mkTalepStateUpdaterService;

    @Autowired
    private FilePersisterService filePersisterService;

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/kararGonderID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/yenikararIT", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ITKararResponse> yenikararIT(@Parameter(required = true, description = "Yuklenecek dosya") @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiIT, @Parameter(required = true, description = "IT Mahkeme Karar Detaylari", schema = @Schema(implementation = ITKararRequest.class, type = "string")) @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) ITKararRequest request, Authentication authentication) {

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("ITKararRequest received:{}, user:{}", request, user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiIT);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("ITKararRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("ITKararRequest file save failed. id:{}", request.getId());
                ITKararResponse response = ITKararResponse.builder().requestId(request.getId()).response(saveResponse).build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            ITKararResponse response = mahkemeKararProcessService.process(request, ITKararResponse.class, MakosUserDetails.fromUserDetails(user));
            log.info("ITKararRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode())).body(response);

        } catch (Exception e) {
            log.error("ITKararRequest failed. id:{}", request.getId(), e);
            ITKararResponse response = ITKararResponse.builder().response(MakosApiResponse.builder().responseCode(MakosResponseCode.FAILED).responseMessage("Internal Error").build()).build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping("/htsMahkemeKararTalepSorgu")
    public ResponseEntity<ITMahkemeKararTalepSorgulamaResponse> htsMahkemeKararTalepSorgu(@NotNull @Valid @RequestBody ITMahkemeKararTalepSorgulamaRequest sorguParam, Authentication authentication) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            String kurumKodu = user.getKullaniciKurum().getValue();
            List<MahkemeKararTalepSorguView> sonucListesi = null;//mahkemeKararTalepService.mahkemeKararTalepSorgu(kurumKodu, sorguParam);

            return ResponseEntity.ok(
                    ITMahkemeKararTalepSorgulamaResponse.builder()
                            .mahkemeKararTalepSorguViewListesi(sonucListesi)
                            .response(
                                    MakosApiResponse.builder()
                                            .responseCode(MakosResponseCode.SUCCESS)
                                            .build())
                            .build());
        } catch (Exception ex) {
            log.error("mahkemeKararTalepSorgu process failed, requestId:{}", sorguParam.hashCode(), ex);
            ITMahkemeKararTalepSorgulamaResponse response = ITMahkemeKararTalepSorgulamaResponse.builder().mahkemeKararTalepSorguViewListesi(null).response(MakosApiResponse.builder().responseCode(MakosResponseCode.FAILED).responseMessage("Sorgu Başarısız").build()).build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }

    @PostMapping("/htsMahkemeKararTalepBilgisi")
    public ResponseEntity<MahkemeKararTalepQueryResponse> htsMahkemeKararTalepBilgisi(@NotNull @Valid @RequestBody MahkemeKararTalepBilgisiRequest sorguParam, Authentication authentication) {
        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();
            log.info("MahkemeKararTalep Detay Bilgi Sorgusu Received:{}, user:{}", sorguParam, user.getUsername());

            Response<MahkemeKarariInfo> mahkemeKarariInfoResp = mahkemeKararTalepService.getMahkemeKararTalepDetails(sorguParam.getMahkemeKararId(), true);
            log.info("MahkemeKararTalep Detay Bilgi Sorgusu Result:{}", mahkemeKarariInfoResp);
            if (mahkemeKarariInfoResp.isSuccess()) {
                return ResponseEntity.ok(MahkemeKararTalepQueryResponse.builder().iDMahkemeKarariInfo(mahkemeKarariInfoResp.getResult()).response(MakosApiResponse.builder().responseCode(MakosResponseCode.SUCCESS).build()).build());
            } else {
                return ResponseEntity.ok(MahkemeKararTalepQueryResponse.builder().response(MakosApiResponse.builder().responseCode(MakosResponseCode.FAILED).responseMessage("MahkemeKararTalep Detay Bilgi Sorgusu Başarısız: ").build()).build());
            }


        } catch (Exception ex) {
            log.error("MahkemeKararTalep Detay Bilgi Sorgusu failed, requestId:{}", sorguParam.hashCode(), ex);
            MahkemeKararTalepQueryResponse response = MahkemeKararTalepQueryResponse.builder().response(MakosApiResponse.builder().responseCode(MakosResponseCode.FAILED).responseMessage("MahkemeKararTalep Detay Bilgi Sorgusu Başarısız").build()).build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


    public String getFullEvrakSavePath(MkTalepRequest request, String fileName) {
        String folderName = request.getId().toString();
        String baseFolder = CommonUtils.appendSubFolder(CommonUtils.getEvrakBasePath(), folderName);
        return CommonUtils.appendFileToPath(baseFolder, fileName);
    }

    public MakosApiResponse saveRequestFile(MkTalepRequest request, MultipartFile file) {
        String filePath = null;
        try {
            // TODO update here if necessary
            filePath = getFullEvrakSavePath(request, file.getOriginalFilename());
            boolean saved = filePersisterService.saveFileToDisk(filePath, file.getBytes());
            if (saved) {
                return MakosApiResponse.builder().responseCode(MakosResponseCode.SUCCESS).responseMessage(filePath).build();
            } else {
                return MakosApiResponse.builder().responseCode(MakosResponseCode.FAILED).responseMessage("Dosya saklama hatasi").build();
            }
        } catch (Exception e) {
            log.error("saveRequestFile failed. id:{}, filePath:{}", request.getId(), filePath, e);
            return MakosApiResponse.builder().responseCode(MakosResponseCode.FAILED).responseMessage("Dosya saklama hatasi").build();
        }
    }

}