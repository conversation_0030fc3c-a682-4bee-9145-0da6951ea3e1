package iym.makos.errors;

import iym.makos.model.MakosResponseCode;

import java.util.UUID;

/*
hata kodları sonra duzenlenecek
* */
public class MakosResponseErrorCodes {

    public static String BILINMEYEN_HATA = "Bilinmeyen Bir Hata Oluştu";
    public static String VERITABANI_HATASI = "Veritabanı Hatası Oluştu.";
    public static String GECERSIZ_PARAMETRE = "Geçersiz Parametre: %s";
    public static String EVRAK_KAYIT_HATASI = "Evrak kayıt Hatası";
    public static String EVRAK_SIRANO_ALINAMADI = "Evrak Sıra No Alınamadı";
    public static String EVRAK_ZATEN_VAR = "Evrak Zaten Kayıtlı. EvrakNo: %s, Evrak Kurum:%s";
    public static String EVRAK_TURU_DESTEKLENMIYOR = "Desteklenmeyen evrak türü.";

    public static String HTSMKTALEP_KAYIT_HATASI = "HTS Mahkeme karar talep kaydı yapılamadı.";
    public static String MKTALEP_KAYIT_HATASI = "Mahkeme karar talep kaydı yapılamadı.";
    public static String MKTALEP_OZELLIK_KAYIT_HATASI = "Mahkeme karar talep özellik kaydı yapılamadı.";
    public static String MKTALEP_SUCTIPI_KAYIT_HATASI = "Mahkeme karar talep suç tipi kaydetme hatası. Suç Tipi Kodu : %s";

    public static String MKTALEP_AIDIYAT_KAYIT_HATASI = "Mahkeme karar talep aidiyat kaydetme hatası. Aidiyat Kodu : %s";
    public static String MKTALEP_AIDIYAT_GUNCELLEME_HATASI = "Mahkeme karar talep aidiyat güncellme hatası. Aidiyat Kodu : %s";
    public static String MKTALEP_HEDEF_KAYIT_HATASI = "Mahkeme karar talep hedef kaydetme hatası. Hedef No : %s Hedef Tipi :%s";
    public static String HTSTALEP_HEDEF_KAYIT_HATASI = "HTS Mahkeme karar talep hedef kaydetme hatası. Hedef No : %s";

    public static String MKTALEP_HEDEF_DETAY_KAYIT_HATASI = "Mahkeme karar talep hedef detay kaydetme hatası. Hedef No : %s Hedef Tipi :%s";
    public static String MKTALEP_DETAY_KAYIT_HATASI = "Mahkeme karar talep detay  kaydetme hatası. ";
    public static String MKTALEP_DETAY_HEDEF_HATASI = "Mahkeme karar talep detay  kaydetme hatası. ";

    public static String MKTALEP_MAHKEMEKODUDETAYKAYDETME_HATASI = "Mahkeme karar mahkeme kodu talep detay  kaydetme hatası. ";
    public static String MKTALEP_MAHKEMEBILGIGUNCELLEME_KAYIT_HATASI = "Mahkeme karar mahkeme kodu talep detay  kaydetme hatası. ";
    public static String MAHKEMEKARARTALEP_HEDEFAIDIYAT_KAYDETMEHATASI = "Hedef aidiyat kaydetme hatası. Hedef No: %s, Aidiyat Kodu: %s";
    public static String MAHKEMEKARARTALEP_HEDEFDETAY_KAYDETMEHATASI = "Hedef detay kaydetme hatası";

    public static String BILINMEYEN_MAHKEME_KARAR_TIPI = "Bilinmeyen mahkeme karar Tipi. Makheme Karar Tipi: %s";
    public static String MAHKEMEBILGISI_BULUNAMADI = "Mahkeme bilgisi bulunamadı. Mahkeme Kodu: %s";
    public static String MAHKEMEKARARTALEP_KAYDETMEHATASI = "Mahkeme karar kaydedilemedi";

    public static String HEDEF_BULUNAMADI = "Hedef bulunmadı. Hedef No: %s, Hedef Tipi:%s ";
    public static String CANAKNO_BOS_OLAMAZ = "Çanak numarası boş olamaz";
    public static String CANAKNO_BOS_OLMALIDIR = "Çanak numarası boş olmalıdır.";
    public static String MAHKEMEKARARTALEP_AIDIYAT_KAYDETMEHATASI = "Mahkeme karar talep aidiyat kaydetme hatası. Aidiyat Kodu : %s";
    public static String MAHKEMEKARARTALEP_SUCTIPI_KAYDETMEHATASI = "Mahkeme karar talep suç tipi kaydetme hatası. Suç tipi Kodu : %s";
    public static String SUCTIPI_BULUNAMADI = "%s koduna sahip şuç tipi bulunamadı.";
    public static String MKTALEP_HEDEFLER_AIDIYAT_KAYDETMEHATASI = "Mahkeme karar talep hedefler aidiyat kaydetme hatası. Hedef No : %s, Aidiyat Kodu : %s";
    public static String MKTALEP_HEDEFLER_DETAY_KAYDETMEHATASI = "Hedefler detay talep kaydetme hatası. Hedef No : %s, Hedef Tipi: %s";
    public static String MKTALEP_HEDEFLERTALEP_KAYDETMEHATASI = "HedeflerTalep kaydetme hatası. Hedef No : %s, Hedef Tipi: %s";

    public static String MK_BULUNAMADI="Mahkeme Karar Bulunamadı: Mahkeme İl/İlçe Kodu: %s Mahkeme Kodu: %s, Karar No: %s, Soruşturma No :%s";
    public static String MK_SUCTIPI_KAYIT_HATASI = "Mahkeme karar  suç tipi kaydetme hatası. MahkemeKararId : %s, SuçTipiKodu : %s";
    public static String MK_SUCTIPI_BULUNAMADI = "Mahkeme karar  suç tipi bulunamadı. MahkemeKararId : %s, SuçTipiKodu : %s";
    public static String MK_SUCTIPI_ZATENVAR = "Mahkeme karar  suç tipi zaten ekli. MahkemeKararId : %s, SuçTipiKodu : %s";


}