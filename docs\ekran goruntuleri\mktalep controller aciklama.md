# Talep Türleri ve İzin Verilen Karar Türleri

Aşa<PERSON>dak<PERSON> tabloda, kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> göndereb<PERSON><PERSON>ği talep türleri ve bu talepler kapsamında iletebilecekleri karar türleri listelenmiştir:

| **Talep Türü**          | **Gönderilebilecek Karar Türleri**                                                                                                                                                                                            |
|-------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| İLETİŞİMİN DENETLENMESİ | - Yeni İletişimin Denetlenmesi Kararı<br>- Uzatma Kararı<br>- Sonlandırma Kararı<br>- Aidiyat Güncelleme Kararı<br>- Suç Tipi Güncelleme Kararı<br>- Mahkeme Bilgisi Güncelleme Kararı<br>- Hedef Bilgileri Güncelleme Kararı |
| İLETİŞİMİN TESPİTİ      | - Yeni İletişimin Tespiti Kararı                                                                                                                                                                                              |
| GENEL EVRAK             | - Genel Evrak Kararı                                                                                                                                                                                                          |

## Karar Gönderme Metodları

Kararlar aşağıdaki metodlar ile gönderilmektedir:

- **İletişimin Denetlenmesi Yeni Karar**
  ```java
  ResponseEntity<IDYeniKararResponse> yeniKararID(
      MultipartFile mahkemeKararDosyasiID,
      IDYeniKararRequest request,
      Authentication authentication
  )


- **İletişimin Denetlenmesi Uzatma Kararı**
  ```java
    ResponseEntity<IDUzatmaKarariResponse> uzatmaKarariID(
    MultipartFile mahkemeKararDosyasiID,
    IDUzatmaKarariRequest request,
    Authentication authentication
    )

- **İletişimin Denetlenmesi Sonlandırma Kararı**
  ```java
  ResponseEntity<IDSonlandirmaKarariResponse> sonlandirmaKarariID(
  MultipartFile mahkemeKararDosyasiID,
  IDSonlandirmaKarariRequest request,
  Authentication authentication
  )

  

- **Yeni İletişimin Tespiti Yeni Karar**
  ```java
  ResponseEntity<ITKararResponse> yenikararIT(
  MultipartFile mahkemeKararDosyasiIT,
  ITKararRequest request,
  Authentication authentication
  )

- **İletişimin Denetlenmesi Aidiyat Güncelleme Kararı**
  ```java  
  ResponseEntity<IDAidiyatBilgisiGuncellemeResponse> aidiyatBilgisiGuncelle(
  MultipartFile mahkemeKararDosyasiID,
  IDAidiyatBilgisiGuncellemeRequest request,
  Authentication authentication
  )

- **İletişimin Denetlenmesi Suç Tipi Güncelleme Kararı**
  ```java
  ResponseEntity<IDSucTipiGuncellemeResponse> sucTipiGuncelle(
  MultipartFile mahkemeKararDosyasiID,
  IDSucTipiGuncellemeRequest request,
  Authentication authentication
  )

- **İletişimin Denetlenmesi Mahkeme Bilgisi Güncelleme Kararı**
  ```java
  ResponseEntity<IDMahkemeKararGuncellemeResponse> mahkemeBilgisiGuncelle(
  MultipartFile mahkemeKararDosyasiID,
  IDMahkemeKararGuncellemeRequest request,
  Authentication authentication
  )

- **İletişimin Denetlenmesi Hedef Bilgileri Güncelleme Kararı**
  ```java 
  ResponseEntity<IDHedefGuncellemeResponse> hedefBilgisiGuncelle(
  MultipartFile mahkemeKararDosyasiID,
  IDHedefGuncellemeRequest request,
  Authentication authentication
  )

<br>
<br>
Kurum kullanıcıları tarafından gönderilen kararlar taslak niteliğinde olup BTK tarafından herhangi bir işlem yapılamaz; 
bu kararlar sadece ilgili kurum kullanıcıları tarafından görülebilir ve kurum bazında işlenecek karar niteliği taşır, 
işlenecek kararların listesi ise belirtilen metod ile alınır.

```java    @PostMapping("/islenecekKararListele")
    ResponseEntity<MahkemeKararTalepIslenecekResponse> islenecekKararListele(
            MahkemeKararTalepIslenecekRequest request, 
            Authentication authentication);
  
```
<br><br>
Gönderilen kararlardan birinin durumu null olduğunda, bu karar taslak niteliğindedir; bu durumda gönderici kararı silebilir veya BTK’ya iletilmek üzere onaylayabilir, her iki işlem de MahkemeKararTalepController sınıfında bulunan talepGuncelle metodu aracılığıyla gerçekleştirilebilir; metodun yapısı aşağıda verilmiştir.

```java
  ResponseEntity<MahkemeKararTalepStateUpdateResponse> talepGuncelle(
        MahkemeKararTalepStateUpdateRequest request,
        Authentication authentication);

```
<br>
<br>
Gönderilen kararın detayını görmek için mahkemeKararTalepBilgisi metodu kullanılır

```java
   ResponseEntity<MahkemeKararTalepQueryResponse> mahkemeKararTalepBilgisi(
        MahkemeKararTalepBilgisiRequest sorguParam, 
        Authentication authentication) 
