package iym.common.service.db;

import iym.common.model.entity.iym.EvrakKayit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;


import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for EvrakKayit entity
 */
public interface DbEvrakKayitService extends GenericDbService<EvrakKayit, Long> {

    boolean existsByEvrakSiraNo(String evrakSiraNo);

    Optional<EvrakKayit> findByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo
            , String evrakGeldigiKurumKodu);

    List<EvrakKayit> findAllByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo
            , String evrakGeldigiKurumKodu);

    Optional<EvrakKayit> findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(String evrakNo
            , String gelIlIlceKodu
            , String evrakGeldigiKurumKodu);

    List<EvrakKayit> findByEvrakTipi(String evrakTipi);

    List<EvrakKayit> findByGirisTarihBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<EvrakKayit> findByDurumu(String durumu);

    List<EvrakKayit> findByHavaleBirim(String havaleBirim);

    List<EvrakKayit> findByAcilmi(String acilmi);

    Page<EvrakKayit> findIslenecekEvraklarByKurumKodu(String kurumKodu, Pageable p);

}
