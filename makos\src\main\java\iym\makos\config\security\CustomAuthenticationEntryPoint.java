package iym.makos.config.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.util.EnvironmentUtil;
import iym.common.util.JsonUtils;
import iym.common.model.api.ProblemDetailBuilder;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ProblemDetail;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final EnvironmentUtil environmentUtil;

    public CustomAuthenticationEntryPoint(EnvironmentUtil environmentUtil) {
        this.environmentUtil = environmentUtil;
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException)
            throws IOException, ServletException {

        response.setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        // Create safe message for production
        String message;
        if (environmentUtil.isDevelopmentEnvironment()) {
            message = "Unauthorized: " + authException.getMessage();
        } else {
            message = "Authentication required";
        }

        // Create ProblemDetail for consistent error response format
        ProblemDetail problemDetail = ProblemDetailBuilder.authenticationError(message);
        problemDetail.setProperty("path", request.getServletPath());

        // Always log full details for debugging
        log.error("Authentication failed. uri:{}, error:{}",
                request.getServletPath(),
                authException.getMessage(),
                authException);

        final ObjectMapper mapper = JsonUtils.getMapper();
        mapper.writeValue(response.getOutputStream(), problemDetail);
        response.getOutputStream().flush();
    }
}
