package iym.common.model.api;

import iym.common.enums.ResponseCode;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;

import java.net.URI;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class ProblemDetailBuilderTest {

    @Test
    void testBasicProblemDetailCreation() {
        // Given
        String detail = "Test error message";
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.forStatus(HttpStatus.BAD_REQUEST)
                .withType(ProblemDetailBuilder.ProblemTypes.VALIDATION_ERROR)
                .withTitle(ProblemDetailBuilder.ProblemTitles.VALIDATION_ERROR)
                .withDetail(detail)
                .withErrorId()
                .build();
        
        // Then
        assertNotNull(problemDetail);
        assertEquals(400, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.VALIDATION_ERROR, problemDetail.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.VALIDATION_ERROR, problemDetail.getTitle());
        assertEquals(detail, problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId"));
    }

    @Test
    void testFromResponseCodeMapping() {
        // Test INVALID_REQUEST mapping
        ProblemDetail invalidRequest = ProblemDetailBuilder.fromResponseCode(ResponseCode.INVALID_REQUEST)
                .withDetail("Invalid request")
                .build();
        
        assertEquals(400, invalidRequest.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.INVALID_REQUEST, invalidRequest.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.INVALID_REQUEST, invalidRequest.getTitle());
        assertNotNull(invalidRequest.getProperties());
        assertEquals("INVALID_REQUEST", invalidRequest.getProperties().get("responseCode"));

        // Test FAILED mapping
        ProblemDetail failed = ProblemDetailBuilder.fromResponseCode(ResponseCode.FAILED)
                .withDetail("Internal error")
                .build();
        
        assertEquals(500, failed.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.INTERNAL_SERVER_ERROR, failed.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.INTERNAL_SERVER_ERROR, failed.getTitle());
        assertNotNull(failed.getProperties());
        assertEquals("FAILED", failed.getProperties().get("responseCode"));
    }

    @Test
    void testCustomProperties() {
        // Given
        UUID errorId = UUID.randomUUID();
        UUID requestId = UUID.randomUUID();
        String customValue = "custom-value";
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.forStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .withErrorId(errorId)
                .withRequestId(requestId)
                .withProperty("customProperty", customValue)
                .withResponseCode(ResponseCode.FAILED)
                .build();
        
        // Then
        assertEquals(errorId.toString(), problemDetail.getProperties().get("errorId"));
        assertEquals(requestId.toString(), problemDetail.getProperties().get("requestId"));
        assertEquals(customValue, problemDetail.getProperties().get("customProperty"));
        assertEquals("FAILED", problemDetail.getProperties().get("responseCode"));
    }

    @Test
    void testConvenienceMethodValidationError() {
        // Given
        String detail = "Validation failed for field 'name'";
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.validationError(detail);
        
        // Then
        assertEquals(400, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.VALIDATION_ERROR, problemDetail.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.VALIDATION_ERROR, problemDetail.getTitle());
        assertEquals(detail, problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId"));
        assertEquals("INVALID_REQUEST", problemDetail.getProperties().get("responseCode"));
    }

    @Test
    void testConvenienceMethodAuthenticationError() {
        // Given
        String detail = "Invalid credentials";
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.authenticationError(detail);
        
        // Then
        assertEquals(401, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.AUTHENTICATION_ERROR, problemDetail.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.AUTHENTICATION_ERROR, problemDetail.getTitle());
        assertEquals(detail, problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId"));
    }

    @Test
    void testConvenienceMethodAuthorizationError() {
        // Given
        String detail = "Access denied";
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.authorizationError(detail);
        
        // Then
        assertEquals(403, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.AUTHORIZATION_ERROR, problemDetail.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.AUTHORIZATION_ERROR, problemDetail.getTitle());
        assertEquals(detail, problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId"));
    }

    @Test
    void testConvenienceMethodInternalServerError() {
        // Given
        String detail = "Database connection failed";
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.internalServerError(detail);
        
        // Then
        assertEquals(500, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.INTERNAL_SERVER_ERROR, problemDetail.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.INTERNAL_SERVER_ERROR, problemDetail.getTitle());
        assertEquals(detail, problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId"));
        assertEquals("FAILED", problemDetail.getProperties().get("responseCode"));
    }

    @Test
    void testConvenienceMethodMakosApiError() {
        // Given
        String detail = "MAKOS API returned error";
        HttpStatus status = HttpStatus.BAD_GATEWAY;
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.makosApiError(detail, status);
        
        // Then
        assertEquals(502, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.MAKOS_API_ERROR, problemDetail.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.MAKOS_API_ERROR, problemDetail.getTitle());
        assertEquals(detail, problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId"));
        assertEquals("FAILED", problemDetail.getProperties().get("responseCode"));
    }

    @Test
    void testConvenienceMethodBusinessLogicError() {
        // Given
        String detail = "Business rule violation";
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.businessLogicError(detail);
        
        // Then
        assertEquals(400, problemDetail.getStatus());
        assertEquals(ProblemDetailBuilder.ProblemTypes.BUSINESS_LOGIC_ERROR, problemDetail.getType());
        assertEquals(ProblemDetailBuilder.ProblemTitles.BUSINESS_LOGIC_ERROR, problemDetail.getTitle());
        assertEquals(detail, problemDetail.getDetail());
        assertNotNull(problemDetail.getProperties().get("errorId"));
        assertEquals("INVALID_REQUEST", problemDetail.getProperties().get("responseCode"));
    }

    @Test
    void testWithInstance() {
        // Given
        URI instance = URI.create("/api/test/123");
        
        // When
        ProblemDetail problemDetail = ProblemDetailBuilder.forStatus(HttpStatus.NOT_FOUND)
                .withInstance(instance)
                .withDetail("Resource not found")
                .build();
        
        // Then
        assertEquals(instance, problemDetail.getInstance());
    }

    @Test
    void testProblemTypeConstants() {
        // Verify all problem type URIs are properly formed
        assertTrue(ProblemDetailBuilder.ProblemTypes.VALIDATION_ERROR.toString().startsWith("https://iym.gov.tr/problems/"));
        assertTrue(ProblemDetailBuilder.ProblemTypes.AUTHENTICATION_ERROR.toString().startsWith("https://iym.gov.tr/problems/"));
        assertTrue(ProblemDetailBuilder.ProblemTypes.AUTHORIZATION_ERROR.toString().startsWith("https://iym.gov.tr/problems/"));
        assertTrue(ProblemDetailBuilder.ProblemTypes.MAKOS_API_ERROR.toString().startsWith("https://iym.gov.tr/problems/"));
        assertTrue(ProblemDetailBuilder.ProblemTypes.INTERNAL_SERVER_ERROR.toString().startsWith("https://iym.gov.tr/problems/"));
        assertTrue(ProblemDetailBuilder.ProblemTypes.INVALID_REQUEST.toString().startsWith("https://iym.gov.tr/problems/"));
        assertTrue(ProblemDetailBuilder.ProblemTypes.BUSINESS_LOGIC_ERROR.toString().startsWith("https://iym.gov.tr/problems/"));
    }
}
