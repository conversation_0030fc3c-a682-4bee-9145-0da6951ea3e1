package iym.common.model.api;

import iym.common.enums.ResponseCode;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;

import java.net.URI;
import java.util.UUID;

/**
 * Builder utility for creating RFC 7807 compliant ProblemDetail instances
 * with IYM-specific extensions and consistent error tracking.
 */
public class ProblemDetailBuilder {

    /**
     * Base URI for IYM problem types
     */
    public static final String PROBLEM_TYPE_BASE_URI = "https://iym.gov.tr/problems/";

    /**
     * Common problem type URIs
     */
    public static final class ProblemTypes {
        public static final URI VALIDATION_ERROR = URI.create(PROBLEM_TYPE_BASE_URI + "validation-error");
        public static final URI AUTHENTICATION_ERROR = URI.create(PROBLEM_TYPE_BASE_URI + "authentication-error");
        public static final URI AUTHORIZATION_ERROR = URI.create(PROBLEM_TYPE_BASE_URI + "authorization-error");
        public static final URI MAKOS_API_ERROR = URI.create(PROBLEM_TYPE_BASE_URI + "makos-api-error");
        public static final URI INTERNAL_SERVER_ERROR = URI.create(PROBLEM_TYPE_BASE_URI + "internal-server-error");
        public static final URI INVALID_REQUEST = URI.create(PROBLEM_TYPE_BASE_URI + "invalid-request");
        public static final URI BUSINESS_LOGIC_ERROR = URI.create(PROBLEM_TYPE_BASE_URI + "business-logic-error");
    }

    /**
     * Common problem titles
     */
    public static final class ProblemTitles {
        public static final String VALIDATION_ERROR = "Validation Error";
        public static final String AUTHENTICATION_ERROR = "Authentication Error";
        public static final String AUTHORIZATION_ERROR = "Authorization Error";
        public static final String MAKOS_API_ERROR = "MAKOS API Error";
        public static final String INTERNAL_SERVER_ERROR = "Internal Server Error";
        public static final String INVALID_REQUEST = "Invalid Request";
        public static final String BUSINESS_LOGIC_ERROR = "Business Logic Error";
    }

    private final ProblemDetail problemDetail;

    private ProblemDetailBuilder(HttpStatus status) {
        this.problemDetail = ProblemDetail.forStatus(status);
    }

    /**
     * Create a new ProblemDetailBuilder with the specified HTTP status
     */
    public static ProblemDetailBuilder forStatus(HttpStatus status) {
        return new ProblemDetailBuilder(status);
    }

    /**
     * Create a ProblemDetailBuilder from ResponseCode enum
     */
    public static ProblemDetailBuilder fromResponseCode(ResponseCode responseCode) {
        HttpStatus status = responseCode.toHttpStatus();
        ProblemDetailBuilder builder = new ProblemDetailBuilder(status);

        // Set default type and title based on ResponseCode
        switch (responseCode) {
            case SUCCESS -> {
                // Success shouldn't create a problem detail, but handle gracefully
                builder.withType(URI.create(PROBLEM_TYPE_BASE_URI + "success"))
                       .withTitle("Success");
            }
            case INVALID_REQUEST -> {
                builder.withType(ProblemTypes.INVALID_REQUEST)
                       .withTitle(ProblemTitles.INVALID_REQUEST);
            }
            case FAILED -> {
                builder.withType(ProblemTypes.INTERNAL_SERVER_ERROR)
                       .withTitle(ProblemTitles.INTERNAL_SERVER_ERROR);
            }
        }

        // Always set the responseCode property for backward compatibility
        builder.withResponseCode(responseCode);

        return builder;
    }

    /**
     * Set the problem type URI
     */
    public ProblemDetailBuilder withType(URI type) {
        problemDetail.setType(type);
        return this;
    }

    /**
     * Set the problem title
     */
    public ProblemDetailBuilder withTitle(String title) {
        problemDetail.setTitle(title);
        return this;
    }

    /**
     * Set the problem detail message
     */
    public ProblemDetailBuilder withDetail(String detail) {
        problemDetail.setDetail(detail);
        return this;
    }

    /**
     * Set the problem instance URI
     */
    public ProblemDetailBuilder withInstance(URI instance) {
        problemDetail.setInstance(instance);
        return this;
    }

    /**
     * Add an error ID for tracking (IYM-specific extension)
     */
    public ProblemDetailBuilder withErrorId(UUID errorId) {
        problemDetail.setProperty("errorId", errorId.toString());
        return this;
    }

    /**
     * Add an error ID for tracking with auto-generated UUID
     */
    public ProblemDetailBuilder withErrorId() {
        return withErrorId(UUID.randomUUID());
    }

    /**
     * Add a request ID for correlation (IYM-specific extension)
     */
    public ProblemDetailBuilder withRequestId(UUID requestId) {
        problemDetail.setProperty("requestId", requestId.toString());
        return this;
    }

    /**
     * Add a custom property
     */
    public ProblemDetailBuilder withProperty(String name, Object value) {
        problemDetail.setProperty(name, value);
        return this;
    }

    /**
     * Add the original ResponseCode for backward compatibility
     */
    public ProblemDetailBuilder withResponseCode(ResponseCode responseCode) {
        problemDetail.setProperty("responseCode", responseCode.name());
        return this;
    }

    /**
     * Build the final ProblemDetail instance
     */
    public ProblemDetail build() {
        return problemDetail;
    }

    /**
     * Convenience method to create a validation error
     */
    public static ProblemDetail validationError(String detail) {
        return forStatus(HttpStatus.BAD_REQUEST)
                .withType(ProblemTypes.VALIDATION_ERROR)
                .withTitle(ProblemTitles.VALIDATION_ERROR)
                .withDetail(detail)
                .withErrorId()
                .withResponseCode(ResponseCode.INVALID_REQUEST)
                .build();
    }

    /**
     * Convenience method to create an authentication error
     */
    public static ProblemDetail authenticationError(String detail) {
        return forStatus(HttpStatus.UNAUTHORIZED)
                .withType(ProblemTypes.AUTHENTICATION_ERROR)
                .withTitle(ProblemTitles.AUTHENTICATION_ERROR)
                .withDetail(detail)
                .withErrorId()
                .build();
    }

    /**
     * Convenience method to create an authorization error
     */
    public static ProblemDetail authorizationError(String detail) {
        return forStatus(HttpStatus.FORBIDDEN)
                .withType(ProblemTypes.AUTHORIZATION_ERROR)
                .withTitle(ProblemTitles.AUTHORIZATION_ERROR)
                .withDetail(detail)
                .withErrorId()
                .build();
    }

    /**
     * Convenience method to create an internal server error
     */
    public static ProblemDetail internalServerError(String detail) {
        return forStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .withType(ProblemTypes.INTERNAL_SERVER_ERROR)
                .withTitle(ProblemTitles.INTERNAL_SERVER_ERROR)
                .withDetail(detail)
                .withErrorId()
                .withResponseCode(ResponseCode.FAILED)
                .build();
    }

    /**
     * Convenience method to create a MAKOS API error
     */
    public static ProblemDetail makosApiError(String detail, HttpStatus status) {
        return forStatus(status)
                .withType(ProblemTypes.MAKOS_API_ERROR)
                .withTitle(ProblemTitles.MAKOS_API_ERROR)
                .withDetail(detail)
                .withErrorId()
                .withResponseCode(status.is5xxServerError() ? ResponseCode.FAILED : ResponseCode.INVALID_REQUEST)
                .build();
    }

    /**
     * Convenience method to create a business logic error
     */
    public static ProblemDetail businessLogicError(String detail) {
        return forStatus(HttpStatus.BAD_REQUEST)
                .withType(ProblemTypes.BUSINESS_LOGIC_ERROR)
                .withTitle(ProblemTitles.BUSINESS_LOGIC_ERROR)
                .withDetail(detail)
                .withErrorId()
                .withResponseCode(ResponseCode.INVALID_REQUEST)
                .build();
    }
}
