package iym.makos.domain.mktaleprequest.dbhandler;

import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.model.entity.iym.talep.MahkemeAidiyatTalep;
import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeSuclarTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.IDYeniKararDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararRequestCommonDbSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararBilgisi;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDYeniKararRequestDetay;
import iym.makos.utils.UtilService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;


@DisplayName("IDYeniKararDBSaveHandlerTest Unit Tests")
@ExtendWith(MockitoExtension.class)
public class IDYeniKararDBSaveHandlerTest extends BaseDomainUnitTest {

    @Mock
    private UtilService utilService;

    @Mock
    private DbEvrakKayitService dbEvrakKayitService;

    //bu siniftaki

    @Mock
    private DbHedeflerTalepService dbHedeflerTalepService;
    @Mock
    private DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    @Mock
    private KararRequestMapper kararRequestMapper;
    @Mock
    private DbMahkemeAidiyatTalepService dbMahkemeAidiyatTalepService;
    @Mock
    private DbMahkemeSuclarTalepService dbMahkemeSuclarTalepService;
    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @Mock
    private MahkemeKararRequestCommonDbSaveHandler mahkemeKararRequestDbSaveHandlerHelper;

    @InjectMocks
    private IDYeniKararDBSaveHandler dbSaveHandler;


    private MahkemeKararTalepIdWithEvrakId talepIdWithEvrakId;
    private IDYeniKararRequest request;
    private MkTalepDbSaveRequest<IDYeniKararRequest> dbSaveRequest;
    private LocalDateTime kayitTarihi;
    private Long kullaniciId;


    @BeforeEach
    void setUp() {

        talepIdWithEvrakId = new MahkemeKararTalepIdWithEvrakId(1L, 2L);

        IDYeniKararRequestDetay requestDetay = IDYeniKararRequestDetay.builder()
                .mahkemeAidiyatKodlari(Collections.singletonList("A1"))
                .mahkemeSucTipiKodlari(Collections.singletonList("S1"))
                .build();

        IDHedefDetay hedefDetay =  IDHedefDetay.builder().build();
        hedefDetay.setHedefAidiyatKodlari(Collections.singletonList("H1"));
        requestDetay.setHedefDetayListesi(Collections.singletonList(hedefDetay));

        request = new IDYeniKararRequest();
        request.setYeniKararRequestDetay(requestDetay);


        kayitTarihi = LocalDateTime.now();
        kullaniciId = 42L;


        talepIdWithEvrakId = createTestMahkemeKararTalepIdWithEvrakId();

        dbSaveRequest = MkTalepDbSaveRequest.<IDYeniKararRequest>builder()
                .kararRequest(request)
                .kullaniciId(kullaniciId)
                .build();
    }

    @Test
    void advanceHandleDbSave_nullTalep_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(null, dbSaveRequest))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("mahkemeKarar/EvrakId boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullRequest_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, null))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("request boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullKullaniciId_throwsException() {
        MkTalepDbSaveRequest<IDYeniKararRequest> dbSaveRequest = MkTalepDbSaveRequest.<IDYeniKararRequest>builder()
                .kararRequest(request)
                .kullaniciId(null)
                .build();
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, dbSaveRequest))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kullaniciId boş olamaz");
    }


    @Test
    void advanceHandleDbSave_ShouldNotThrowAnyException() {
        HedeflerTalep hedeflerTalep = HedeflerTalep.builder()
                .id(10L).hedefNo("Hedef1").hedefTipi(100).build();

        when(kararRequestMapper.toHedeflerTalep(any(), anyLong(), anyLong(), any())).thenReturn(hedeflerTalep);
        when(dbHedeflerTalepService.save(any())).thenReturn(hedeflerTalep);
        when(dbHedeflerAidiyatTalepService.save(any())).thenAnswer(inv -> inv.getArgument(0));
        when(dbMahkemeAidiyatTalepService.save(any())).thenAnswer(inv -> inv.getArgument(0));
        when(dbMahkemeSuclarTalepService.save(any())).thenAnswer(inv -> inv.getArgument(0));

        assertThatCode(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, dbSaveRequest))
                .doesNotThrowAnyException();

        verify(dbHedeflerTalepService).save(any(HedeflerTalep.class));
        verify(dbHedeflerAidiyatTalepService).save(any(HedeflerAidiyatTalep.class));
        verify(dbMahkemeAidiyatTalepService).save(any(MahkemeAidiyatTalep.class));
        verify(dbMahkemeSuclarTalepService).save(any(MahkemeSuclarTalep.class));
    }

    @Test
    void advanceHandleDbSave_ShouldSuccess() {
        HedeflerTalep hedeflerTalep = HedeflerTalep.builder()
                .id(10L).hedefNo("Hedef1").hedefTipi(100).build();

        when(kararRequestMapper.toHedeflerTalep(any(), anyLong(), anyLong(), any())).thenReturn(hedeflerTalep);
        when(dbHedeflerTalepService.save(any())).thenReturn(hedeflerTalep);
        when(dbHedeflerAidiyatTalepService.save(any())).thenAnswer(inv -> inv.getArgument(0));
        when(dbMahkemeAidiyatTalepService.save(any())).thenAnswer(inv -> inv.getArgument(0));
        when(dbMahkemeSuclarTalepService.save(any())).thenAnswer(inv -> inv.getArgument(0));

        assertThatCode(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, dbSaveRequest))
                .doesNotThrowAnyException();

        verify(dbHedeflerTalepService).save(any(HedeflerTalep.class));
        verify(dbHedeflerAidiyatTalepService).save(any(HedeflerAidiyatTalep.class));
        verify(dbMahkemeAidiyatTalepService).save(any(MahkemeAidiyatTalep.class));
        verify(dbMahkemeSuclarTalepService).save(any(MahkemeSuclarTalep.class));
    }


    private EvrakDetay createValidEvrakDetay() {
        return EvrakDetay.builder()
                .evrakNo("TEST-EVRAK-001")
                .evrakTarihi(LocalDateTime.now())
                .evrakKurumKodu("TEST-KURUM")
                .evrakTuru(iym.common.enums.EvrakTuru.ILETISIMIN_DENETLENMESI)
                .geldigiIlIlceKodu("0601")
                .acilmi(false)
                .build();
    }

    private MahkemeKararBilgisi createValidMahkemeKararBilgisi() {
        return MahkemeKararBilgisi.builder()
                .mahkemeKararTipi(iym.common.enums.MahkemeKararTip.ADLI_HAKIM_KARARI)
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .build();
    }

    private iym.makos.model.api.MahkemeKararDetay createValidMahkemeKararDetay() {
        return iym.makos.model.api.MahkemeKararDetay.builder()
                .mahkemeKodu("TEST-MAHKEME")
                .mahkemeKararNo("TEST-KARAR-001")
                .mahkemeIlIlceKodu("0601")
                .sorusturmaNo("TEST-SORUSTURMA")
                .build();
    }

}


