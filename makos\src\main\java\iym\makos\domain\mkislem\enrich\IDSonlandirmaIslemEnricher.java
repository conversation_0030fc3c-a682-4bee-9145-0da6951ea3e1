package iym.makos.domain.mkislem.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.mkislem.*;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mk.DbHedeflerIslemService;
import iym.common.service.db.mkislem.*;
import iym.common.util.CommonUtils;

import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDSonlandirmaKararDetay;
import iym.makos.model.dto.view.info.*;
import iym.makos.model.dto.view.mapper.HedeflerIslemEnricherMapper;
import iym.makos.model.dto.view.mapper.KararAidiyatIslemEnricherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class IDSonlandirmaIslemEnricher implements MKIslemEnricher {

    private final DbHedeflerIslemService dbHedeflerIslemService;
    private final HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper;
    private final DbHedeflerAidiyatIslemService dbHedeflerAidiyatService;
    private final DbMahkemeAidiyatIslemService dbMahkemeAidiyatService;
    private final KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper;
    private final DbMahkemeSuclarIslemService dbMahkemeSuclarService;
    private final DbSucTipiService dbSucTipiService;

    private final DbIllerService dbIllerService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    private final DbHedeflerDetayIslemService dbHedeflerDetayService;
    private final DbDetayMahkemeKararIslemService dbDetayMahkemeKararIslemService;

    @Autowired
    public IDSonlandirmaIslemEnricher(
            DbHedeflerIslemService dbHedeflerIslemService
            , HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper
            , DbHedeflerAidiyatIslemService dbHedeflerAidiyatService
            , DbMahkemeAidiyatIslemService dbMahkemeAidiyatService
            , KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper
            , DbMahkemeSuclarIslemService dbMahkemeSuclarService
            , DbSucTipiService dbSucTipiService
            , DbHedeflerDetayIslemService dbHedeflerDetayService
            , DbDetayMahkemeKararIslemService dbDetayMahkemeKararIslemService
            , DbIllerService dbIllerService
            , DbMahkemeBilgiService dbMahkemeBilgiService

    ) {
        this.dbHedeflerIslemService = dbHedeflerIslemService;
        this.hedeflerIslemEnricherMapper = hedeflerIslemEnricherMapper;
        this.dbHedeflerAidiyatService = dbHedeflerAidiyatService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;
        this.iDKararAidiyatIslemEnricherMapper = iDKararAidiyatIslemEnricherMapper;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbSucTipiService = dbSucTipiService;
        this.dbHedeflerDetayService = dbHedeflerDetayService;
        this.dbDetayMahkemeKararIslemService = dbDetayMahkemeKararIslemService;
        this.dbIllerService = dbIllerService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {
        try {
            Long mahkemeKararIslemId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararIslemId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararIslemId yok");
            }

            //enrich only IDYeniKararDetay

            //Kararin Hedefler Listesi
            List<HedeflerIslem> hedeflerList = dbHedeflerIslemService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            List<SonlandirmaKarariHedefInfo> sonlandirmaKarariHedefList = enrichHedefler(hedeflerList);

            //Kararin Aidiyat Listsi
            List<MahkemeAidiyatIslem> aidiyatListesi = dbMahkemeAidiyatService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            List<IDKarariAidiyatInfo> iDKarariAidiyatInfoList = enrichAidiyatListesi(aidiyatListesi);

            //Kararin Suc Tipleri
            List<MahkemeSuclarIslem> sucLitesi = dbMahkemeSuclarService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            List<IDKarariSucTipiInfo> sucTipleriDTOList = enrichSucTipleri(sucLitesi, mahkemeKararIslemId);

            //IDMahkemeKarariInfo'in enrich edilecek nesnesi : MkIslemIDYeniKararDTO
            IDSonlandirmaKararDetay sonlandirmaKararDetay = IDSonlandirmaKararDetay.builder()
                    .hedefListesi(sonlandirmaKarariHedefList)
                    .aidiyatListesi(iDKarariAidiyatInfoList)
                    .sucTipleri(sucTipleriDTOList)
                    .build();
            mahkemeKararIslem.setIDSonlandirmaKararDetay(sonlandirmaKararDetay);
            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("IDSonlandirmaIslemEnricher failed. mahkemeKararIslemId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }

    private List<IDKarariSucTipiInfo> enrichSucTipleri(List<MahkemeSuclarIslem> kararSucTipleri, Long mahkemeKararIslemId) {
        List<IDKarariSucTipiInfo> result = new ArrayList<>();
        List<SucTipi> sucTipiListesi = dbSucTipiService.getByMahkemeIslemId(mahkemeKararIslemId);
        CommonUtils.safeList(kararSucTipleri).forEach(mahkemeKararSucTipi -> {

            SucTipi sucTipi = sucTipiListesi.stream()
                    .filter(s -> s.getSucTipiKodu().equals(mahkemeKararSucTipi.getSucTipKodu()))
                    .findFirst()
                    .orElse(null);

            SucTipiInfo sucTipiInfo = SucTipiInfo.builder()
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipAdi(sucTipi != null ? sucTipi.getAciklama() : "")
                    .build();

            IDKarariSucTipiInfo iDKarariSucTipiInfo = IDKarariSucTipiInfo.builder()
                    .id(mahkemeKararSucTipi.getId())
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipi(sucTipiInfo)
                    .mahkemeKararId(mahkemeKararIslemId)
                    .build();

            result.add(iDKarariSucTipiInfo);

        });

        return result;
    }

    private List<SonlandirmaKarariHedefInfo> enrichHedefler(List<HedeflerIslem> list) {
        List<SonlandirmaKarariHedefInfo> result = new ArrayList<>();
        list.forEach(hedeflerIslem -> {

            //Her bir hedefin(HedefIslem) hangi mahkeme karardaki hedefi sonlandirmak istediginin blunmasi icin
            //HedeflerDetayIslem tablosu  kullaniliarak DetayMahkemeKararIslem bulunuyor
            //Bu sekilde o hedefin iliskli oldugu mahkeme karari bulunmus oluyor.
            //Tablo yapısı daha onceden boyle tasarlandigi icin boyle bir yol benimsenmistir.


            //bu sonlandirmaya konu olan esas mahkeme karar bilgilerini bul(DetayMahkemeKararIslem de saklidir)
            DetayMahkemeKararIslem detayMahkemeKarar = null;
            Optional<HedeflerDetayIslem> hedeflerDetayOpt = dbHedeflerDetayService.findHedeflerDetayIslem(hedeflerIslem.getMahkemeKararIslemId(), hedeflerIslem.getHedefNo(), hedeflerIslem.getHedefTipi());
            if (hedeflerDetayOpt.isPresent()) {
                Long detayMahkemeKararIslemId = hedeflerDetayOpt.get().getDetayMahkemeKararIslemId();
                Optional<DetayMahkemeKararIslem> detayMahkemeKararOpt = dbDetayMahkemeKararIslemService.findById(detayMahkemeKararIslemId);
                if (detayMahkemeKararOpt.isPresent()) {
                    detayMahkemeKarar = detayMahkemeKararOpt.get();
                }
            }
            DetayMahkemeKararInfo iliskiliMahkemeKararDetay = getMahkemeKararDetayInfo(detayMahkemeKarar);

            //Hedefin aidiyat listesi.
            List<HedeflerAidiyatIslem> hedeflerAidiyatList = dbHedeflerAidiyatService.findByHedeflerIslemId(hedeflerIslem.getId());
            List<IDHedefAidiyatInfo> aidiyatList = hedeflerIslemEnricherMapper.toIDHedefAidiyatInfoListIslem(hedeflerAidiyatList);

            SonlandirmaKarariHedefInfo sonlandirmaKarariHedefInfo = hedeflerIslemEnricherMapper.toSonlandirmaKarariHedefInfo(hedeflerIslem);
            sonlandirmaKarariHedefInfo.setIliskiliMahkemeKararInfo(iliskiliMahkemeKararDetay);
            sonlandirmaKarariHedefInfo.setHedefAidiyatListesi(aidiyatList);

            result.add(sonlandirmaKarariHedefInfo);
        });

        return result;
    }

    private DetayMahkemeKararInfo getMahkemeKararDetayInfo(DetayMahkemeKararIslem detay) {
        if (detay == null) {
            return null;
        }
        //Mahkeme Adi
        Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(detay.getMahkemeKodu());

        //il/ilce Adi
        Optional<Iller> ilIlceOpt = dbIllerService.findByIlIlceKodu(detay.getMahkemeIlIlceKodu());

        return DetayMahkemeKararInfo.builder()
                .mahkemeKararNo(detay.getMahkemeKararNo())
                .sorusturmaNo(detay.getSorusturmaNo())
                .mahkemeIlIlceKodu(detay.getMahkemeIlIlceKodu())
                .ilIlceAdi(ilIlceOpt.isPresent() ? ilIlceOpt.get().getIlceAdi() : "")
                .mahkemeKodu(detay.getMahkemeKodu())
                .mahkemeAdi(mahkemeBilgiOpt.isPresent() ? mahkemeBilgiOpt.get().getMahkemeAdi() : "")
                .build();
    }

    private List<IDKarariAidiyatInfo> enrichAidiyatListesi(List<MahkemeAidiyatIslem> aidiyatListesi) {
        return iDKararAidiyatIslemEnricherMapper.toIDKarariAidiyatListIslem(aidiyatListesi);
    }

}



