package iym.makos.domain.mktalep.requestprocessor.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDCokluKararTipliKararRequest;
import iym.makos.model.dto.mktalep.request.id.IDCokluKararTipliKararResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IDCokluKararTipliKararRequestProcessor extends MakosRequestProcessorBase<IDCokluKararTipliKararRequest, IDCokluKararTipliKararResponse> {

    @Override
    public IDCokluKararTipliKararResponse process(IDCokluKararTipliKararRequest request, MakosUserDetails islemYapanKullanici) {

        MakosApiResponse preCheck = preCheck(request, islemYapanKullanici);
        log.error("IDCokluKararTipliKararRequest process failed: reason:{}", preCheck.getResponseMessage());
        if (preCheck.getResponseCode() != MakosResponseCode.SUCCESS) {
            return IDCokluKararTipliKararResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage(preCheck.getResponseMessage())
                            .build())
                    .build();
        }

        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {
                return IDCokluKararTipliKararResponse.builder()
                        .requestId(request.getId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            // save the request
            MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> saveRequest = MkTalepDbSaveRequest.<IDCokluKararTipliKararRequest>builder()
                    .kararRequest(request)
                    .kullaniciId(islemYapanKullanici.getUserId())
                    .build();
            MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = requestSaver.kaydet(saveRequest);

            return IDCokluKararTipliKararResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepIdWithEvrakId.getEvrakId())
                    .mahkemeKararTalepId(mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId())
                    .build();

        } catch (MakosResponseException ex) {
            // Let MakosResponseException propagate with its detailed message
            log.error("IDCokluKararTipliKararRequest process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDCokluKararTipliKararResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage(ex.getFormattedMessage())
                            .build())
                    .build();
        } catch (Exception ex) {
            log.error("IDCokluKararTipliKararRequest process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDCokluKararTipliKararResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        }

    }
}
