package iym.makos.domain.mkislem.enrich;

import iym.common.enums.KararTuru;
import iym.common.enums.ResultCode;
import iym.common.enums.SureTip;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.mkislem.HedeflerAidiyatIslem;
import iym.common.model.entity.iym.mkislem.HedeflerIslem;
import iym.common.model.entity.iym.mkislem.MahkemeAidiyatIslem;
import iym.common.model.entity.iym.mkislem.MahkemeSuclarIslem;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mk.DbHedeflerIslemService;
import iym.common.service.db.mkislem.DbHedeflerAidiyatIslemService;
import iym.common.service.db.mkislem.DbMahkemeAidiyatIslemService;
import iym.common.service.db.mkislem.DbMahkemeSuclarIslemService;
import iym.common.util.CommonUtils;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import iym.makos.model.dto.view.IDYeniKararDetay;
import iym.makos.model.dto.view.info.*;
import iym.makos.model.dto.view.mapper.HedeflerIslemEnricherMapper;
import iym.makos.model.dto.view.mapper.KararAidiyatIslemEnricherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class YeniKararIDIslemEnricher implements MKIslemEnricher {

    private final DbHedeflerIslemService dbHedeflerIslemService;
    private final HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper;
    private final DbHedeflerAidiyatIslemService dbHedeflerAidiyatService;
    private final DbMahkemeAidiyatIslemService dbMahkemeAidiyatService;
    private final KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper;
    private final DbMahkemeSuclarIslemService dbMahkemeSuclarService;
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public YeniKararIDIslemEnricher(
            DbHedeflerIslemService dbHedeflerIslemService
            , HedeflerIslemEnricherMapper hedeflerIslemEnricherMapper
            , DbHedeflerAidiyatIslemService dbHedeflerAidiyatService
            , DbMahkemeAidiyatIslemService dbMahkemeAidiyatService
            , KararAidiyatIslemEnricherMapper iDKararAidiyatIslemEnricherMapper
            , DbMahkemeSuclarIslemService dbMahkemeSuclarService
            , DbSucTipiService dbSucTipiService

    ) {
        this.dbHedeflerIslemService = dbHedeflerIslemService;
        this.hedeflerIslemEnricherMapper = hedeflerIslemEnricherMapper;
        this.dbHedeflerAidiyatService = dbHedeflerAidiyatService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;
        this.iDKararAidiyatIslemEnricherMapper = iDKararAidiyatIslemEnricherMapper;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    public KararTuru getSupportedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }

    @Override
    public Response<String> enrich(MahkemeKarariInfo mahkemeKararIslem) {
        try {
            Long mahkemeKararIslemId = mahkemeKararIslem.getMahkemeKararId();
            if (mahkemeKararIslemId == null) {
                return new Response<>(ResultCode.FAILED, "MahkemeKararIslemId yok");
            }

            //enrich only IDYeniKararDetay

            //Kararin Hedefler Listesi
            List<HedeflerIslem> hedeflerIslemList = dbHedeflerIslemService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            List<YeniIDKarariHedefInfo> iDYeniHedefInfoList = enrichHedefler(hedeflerIslemList);

            //Kararin Aidiyat Listsi
            List<MahkemeAidiyatIslem> aidiyatListesi = dbMahkemeAidiyatService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            List<IDKarariAidiyatInfo> iDKarariAidiyatInfoList = enrichAidiyatListesi(aidiyatListesi);

            //Kararin Suc Tipleri
            List<MahkemeSuclarIslem> sucLitesi = dbMahkemeSuclarService.findByMahkemeKararIslemId(mahkemeKararIslemId);
            List<IDKarariSucTipiInfo> sucTipleriDTOList = enrichSucTipleri(sucLitesi, mahkemeKararIslemId);

            //IDMahkemeKarariInfo'in enrich edilecek nesnesi : MkIslemIDYeniKararDTO
            IDYeniKararDetay yeniKararDetay = IDYeniKararDetay.builder()
                    .hedefListesi(iDYeniHedefInfoList)
                    .hedefListesi(iDYeniHedefInfoList)
                    .mahkemeAidiyatlari(iDKarariAidiyatInfoList)
                    .sucTipleri(sucTipleriDTOList)
                    .build();
            mahkemeKararIslem.setIDYeniKararDetay(yeniKararDetay);

            return new Response<>(ResultCode.SUCCESS);
        } catch (Exception ex) {
            log.error("YeniKararIDEnricher failed. mahkemeKararIslemId:{}", mahkemeKararIslem.getMahkemeKararId(), ex);
            return new Response<>(ResultCode.FAILED, "Internal Error");
        }
    }

    private List<IDKarariSucTipiInfo> enrichSucTipleri(List<MahkemeSuclarIslem> kararSucTipleri, Long mahkemeKararIslemId) {
        List<IDKarariSucTipiInfo> result = new ArrayList<>();
        List<SucTipi> sucTipiListesi = dbSucTipiService.getByMahkemeIslemId(mahkemeKararIslemId);
        CommonUtils.safeList(kararSucTipleri).forEach(mahkemeKararSucTipi -> {

            SucTipi sucTipi = sucTipiListesi.stream()
                    .filter(s -> s.getSucTipiKodu().equals(mahkemeKararSucTipi.getSucTipKodu()))
                    .findFirst()
                    .orElse(null);

            SucTipiInfo sucTipiInfo = SucTipiInfo.builder()
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipAdi(sucTipi != null ? sucTipi.getAciklama() : "")
                    .build();

            IDKarariSucTipiInfo iDKarariSucTipiInfo = IDKarariSucTipiInfo.builder()
                    .id(mahkemeKararSucTipi.getId())
                    .sucTipiKodu(mahkemeKararSucTipi.getSucTipKodu())
                    .sucTipi(sucTipiInfo)
                    .mahkemeKararId(mahkemeKararIslemId)
                    .build();

            result.add(iDKarariSucTipiInfo);

        });

        return result;
    }

    private List<YeniIDKarariHedefInfo> enrichHedefler(List<HedeflerIslem> list) {
        List<YeniIDKarariHedefInfo> result = new ArrayList<>();
        list.forEach(hedeflerIslem -> {

            IDHedefInfo iDHedefInfo = hedeflerIslemEnricherMapper.toIDHedefInfo(hedeflerIslem);

            YeniIDKarariHedefInfo yeniIDKarariHedefInfo = hedeflerIslemEnricherMapper.toYeniIDKarariHedefInfo(hedeflerIslem);

            //Hedeflerin aidiyatlarini set et.
            List<HedeflerAidiyatIslem> hedeflerAidiyatList = dbHedeflerAidiyatService.findByHedeflerIslemId(hedeflerIslem.getId());
            List<IDHedefAidiyatInfo> aidiyatList = hedeflerIslemEnricherMapper.toIDHedefAidiyatInfoListIslem(hedeflerAidiyatList);

            YeniIDKarariHedefInfo iDYeniHedefDetayInfo = YeniIDKarariHedefInfo.builder()
                    .hedefBilgileri(iDHedefInfo)
                    .baslamaTarihi(hedeflerIslem.getBaslamaTarihi())
                    .canakNo(hedeflerIslem.getCanakNo())
                    .sure(hedeflerIslem.getSuresi())
                    .sureTip(SureTip.fromValue(hedeflerIslem.getSureTipi()))
                    .hedefAidiyatListesi(aidiyatList)
                    .build();

            result.add(iDYeniHedefDetayInfo);
        });

        return result;
    }

    private List<IDKarariAidiyatInfo> enrichAidiyatListesi(List<MahkemeAidiyatIslem> aidiyatListesi) {
        List<IDKarariAidiyatInfo> result = new ArrayList<>();

        result = iDKararAidiyatIslemEnricherMapper.toIDKarariAidiyatListIslem(aidiyatListesi);

        return result;
    }

}



