package iym.makos.model.dto.mktalep.query;

import iym.makos.model.MakosRequestResponse;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class MahkemeKararTalepQueryResponse extends MakosRequestResponse {

    private MahkemeKarariInfo iDMahkemeKarariInfo;
    //private MahkemeKararTalepViewDTO mahkemeKararTalepViewDTO;
}