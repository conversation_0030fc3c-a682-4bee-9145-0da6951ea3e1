package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.GuncellemeTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.SucTipi;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.mk.MahkemeSuclar;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mk.DbMahkemeSuclarService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.SucTipiGuncellemeDetay;
import iym.makos.model.api.SucTipiGuncellemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.IDSucTipiGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDSucTipiGuncellemeRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class IDSucTipiGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDSucTipiGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbSucTipiService dbSucTipiService;
    private DbMahkemeSuclarService dbMahkemeSuclarService;


    @Autowired
    public IDSucTipiGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeSuclarService dbMahkemeSuclarService
            , DbSucTipiService dbSucTipiService
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    protected ValidationResult doValidate(IDSucTipiGuncellemeRequest request) {

        try {
            ValidationResult validationResult = new ValidationResult(true);

            MahkemeKararTip mahkemeKararTip = request.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            IDSucTipiGuncellemeRequestDetay kararRequestDetay = request.getSucTipiGuncellemeRequestDetay();

            if (kararRequestDetay == null) {
                validationResult.addFailedReason("SucTipiGuncellemeRequestDetay boş olamaz");
                return validationResult;
            }

            for (SucTipiGuncellemeKararDetay sucTipiGuncellemeKararDetay : kararRequestDetay.getSucTipiGuncellemeKararDetayListesi()) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = sucTipiGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkili mahkeme karar boş olamaz.");
                } else {
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu(), iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {
                        MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();
                        for (SucTipiGuncellemeDetay sucTipiGuncellemeDetay : CommonUtils.safeList(sucTipiGuncellemeKararDetay.getSucTipiGuncellemeDetayListesi())) {
                            String sucTipiKodu = sucTipiGuncellemeDetay.getSucTipiKodu();

                            if (sucTipiGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.CIKAR) {
                                //Çıkarılacak suç tipi ilgili kararda var mı?
                                Optional<MahkemeSuclar> existingMahkemeKararSucOpt = dbMahkemeSuclarService.findByMahkemeKararIdAndSucTipKodu(iliskiliMahkemeKarar.getId(), sucTipiKodu);
                                if (existingMahkemeKararSucOpt.isEmpty()) {
                                    String errMsg = String.format(MakosResponseErrorCodes.MK_SUCTIPI_BULUNAMADI, iliskiliMahkemeKarar.getId(), sucTipiKodu);
                                    validationResult.addFailedReason(errMsg);
                                }
                            } else {
                                //Eklenecek suç tipi var mı?
                                Optional<SucTipi> sucTipiOpt = dbSucTipiService.findBySucTipiKodu(sucTipiKodu);
                                if (sucTipiOpt.isEmpty()) {
                                    String errMsg = String.format(MakosResponseErrorCodes.SUCTIPI_BULUNAMADI, sucTipiKodu);
                                    validationResult.addFailedReason(errMsg);
                                }
                            }
                        }
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME;
    }
}

