package iym.makos.errors;

import iym.makos.model.MakosResponseCode;

public class HedefNotFoundException extends MakosResponseException{
    public HedefNotFoundException(String message) {
        super(message);
    }

    public HedefNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public HedefNotFoundException(String errorCode, Object... args) {
        super(errorCode, args);
    }

    public HedefNotFoundException(MakosResponseCode makosResponseCode, String errorCode, Object... args) {
        super(makosResponseCode, errorCode, args);
    }
}