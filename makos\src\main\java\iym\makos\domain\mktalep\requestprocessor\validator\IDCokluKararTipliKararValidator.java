package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.enums.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mktalep.request.id.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IDCokluKararTipliKararValidator extends MahkemeKararRequestValidatorBase<IDCokluKararTipliKararRequest> {

    private final IMahkemeKararRequestValidator<IDYeniKararRequest> yeniKararValidator;
    private final IMahkemeKararRequestValidator<IDUzatmaKarariRequest> uzatmaKarariValidator;
    private final IMahkemeKararRequestValidator<IDSonlandirmaKarariRequest> sonlandirmaKarariValidator;
    private final IMahkemeKararRequestValidator<IDAidiyatBilgisiGuncellemeRequest> aidiyatBilgisiGuncellemeValidator;
    private final IMahkemeKararRequestValidator<IDHedefGuncellemeRequest> hedefGuncellemeValidator;

    @Autowired
    public IDCokluKararTipliKararValidator(
            IMahkemeKararRequestValidator<IDYeniKararRequest> yeniKararValidator,
            IMahkemeKararRequestValidator<IDUzatmaKarariRequest> uzatmaKarariValidator,
            IMahkemeKararRequestValidator<IDSonlandirmaKarariRequest> sonlandirmaKarariValidator,
            IMahkemeKararRequestValidator<IDAidiyatBilgisiGuncellemeRequest> aidiyatBilgisiGuncellemeValidator,
            IMahkemeKararRequestValidator<IDHedefGuncellemeRequest> hedefGuncellemeValidator) {
        this.yeniKararValidator = yeniKararValidator;
        this.uzatmaKarariValidator = uzatmaKarariValidator;
        this.sonlandirmaKarariValidator = sonlandirmaKarariValidator;
        this.aidiyatBilgisiGuncellemeValidator = aidiyatBilgisiGuncellemeValidator;
        this.hedefGuncellemeValidator = hedefGuncellemeValidator;
    }

    @Override
    protected ValidationResult doValidate(IDCokluKararTipliKararRequest request) {

        try {
            log.info("IDCokluKararTipliKararValidator Validation");

            ValidationResult validationResult = new ValidationResult(true);

            if (request.getYeniKararRequestDetay() != null) {
                IDYeniKararRequest kararRequest = IDYeniKararRequest.builder()
                        .mahkemeKararBilgisi(request.getMahkemeKararBilgisi())
                        .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR)
                        .yeniKararRequestDetay(request.getYeniKararRequestDetay())
                        .build();
                ValidationResult validateResult = yeniKararValidator.validate(kararRequest);
                if (!validateResult.isValid()) {
                    validateResult.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (request.getUzatmaKarariRequestDetay() != null) {
                IDUzatmaKarariRequest kararRequest = IDUzatmaKarariRequest.builder()
                        .mahkemeKararBilgisi(request.getMahkemeKararBilgisi())
                        .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                        .uzatmaKarariRequestDetay(request.getUzatmaKarariRequestDetay())
                        .build();
                ValidationResult validateResult = uzatmaKarariValidator.validate(kararRequest);
                if (!validateResult.isValid()) {
                    validateResult.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (request.getSonlandirmaKarariRequestDetay() != null) {
                IDSonlandirmaKarariRequest kararRequest = IDSonlandirmaKarariRequest.builder()
                        .mahkemeKararBilgisi(request.getMahkemeKararBilgisi())
                        .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                        .sonlandirmaKarariRequestDetay(request.getSonlandirmaKarariRequestDetay())
                        .build();
                ValidationResult validateResult = sonlandirmaKarariValidator.validate(kararRequest);
                if (!validateResult.isValid()) {
                    validateResult.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (request.getAidiyatBilgisiGuncellemeRequestDetay() != null) {
                IDAidiyatBilgisiGuncellemeRequest kararRequest = IDAidiyatBilgisiGuncellemeRequest.builder()
                        .mahkemeKararBilgisi(request.getMahkemeKararBilgisi())
                        .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME)
                        .aidiyatBilgisiGuncellemeRequestDetay(request.getAidiyatBilgisiGuncellemeRequestDetay())
                        .build();
                ValidationResult validateResult = aidiyatBilgisiGuncellemeValidator.validate(kararRequest);
                if (!validateResult.isValid()) {
                    validateResult.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            if (request.getHedefGuncellemeRequestDetay() != null) {
                IDHedefGuncellemeRequest kararRequest = IDHedefGuncellemeRequest.builder()
                        .mahkemeKararBilgisi(request.getMahkemeKararBilgisi())
                        .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME)
                        .hedefGuncellemeRequestDetay(request.getHedefGuncellemeRequestDetay())
                        .build();
                ValidationResult validateResult = hedefGuncellemeValidator.validate(kararRequest);
                if (!validateResult.isValid()) {
                    validateResult.getReasons().forEach(validationResult::addFailedReason);
                }
            }

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_COKLU_KARAR_TIPLI_KARAR;
    }

}

