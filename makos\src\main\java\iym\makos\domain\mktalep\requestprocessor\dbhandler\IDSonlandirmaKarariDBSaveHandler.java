package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.HedefNotFoundException;
import iym.makos.errors.MahkemeKararNotFoundException;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.IDSonlandirmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDSonlandirmaKarariRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDSonlandirmaKarariDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDSonlandirmaKarariRequest> {
    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbHedeflerTalepService dbHedeflerTalepService;
    private final DbHedeflerDetayTalepService dbHedeflerDetayTalepService;
    private final DbHedeflerService dbHedeflerService;
    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    private final KararRequestMapper kararRequestMapper;


    @Autowired
    public IDSonlandirmaKarariDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService
            , DbHedeflerTalepService dbHedeflerTalepService
            , DbHedeflerDetayTalepService dbHedeflerDetayTalepService
            , DbHedeflerService dbHedeflerService
            , DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService
            , KararRequestMapper kararRequestMapper
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbHedeflerTalepService = dbHedeflerTalepService;
        this.dbHedeflerDetayTalepService = dbHedeflerDetayTalepService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbHedeflerAidiyatTalepService = dbHedeflerAidiyatTalepService;
        this.kararRequestMapper = kararRequestMapper;
    }

    @Override
    public void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDSonlandirmaKarariRequest> request) {

        validateRequest(request);

        IDSonlandirmaKarariRequest kararRequest = request.getKararRequest();

        LocalDateTime saveDateTime = LocalDateTime.now();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();

        IDSonlandirmaKarariRequestDetay karariRequestDetay = kararRequest.getSonlandirmaKarariRequestDetay();

        CommonUtils.safeList(karariRequestDetay.getHedefDetayListesi()).forEach(iDHedefDetay -> {


            String hedefNo = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefNo();
            HedefTip hedefTipi = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefTip();

            MahkemeKararDetay ilgiliMahhemeKararDetay = iDHedefDetay.getIlgiliMahkemeKararDetayi();

            DetayMahkemeKararTalep dMahkemeKararTalep = null;

            Optional<DetayMahkemeKararTalep> savedDMahkemeKararTalepOpt = dbDetayMahkemeKararTalepService.findByMahkemeKararBilgileri(evrakId
                    , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                    , ilgiliMahhemeKararDetay.getSorusturmaNo()
                    , ilgiliMahhemeKararDetay.getMahkemeKodu());

            if(!savedDMahkemeKararTalepOpt.isPresent()){

                MahkemeKarar iliskiliMahkemeKarar = dbMahkemeKararService.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo()).orElseThrow(() -> new MahkemeKararNotFoundException(
                        String.format(MakosResponseErrorCodes.MK_BULUNAMADI
                                , ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                                , ilgiliMahhemeKararDetay.getMahkemeKodu()
                                , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                                , ilgiliMahhemeKararDetay.getSorusturmaNo()
                        )
                ));

                //Uzatma Hedefi uzatmaya konu karar listesinin hedeflerinde var mi?
                Hedefler iliskiliHedef = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId()
                        , hedefNo, hedefTipi.getHedefKodu()).orElseThrow(() -> new HedefNotFoundException(
                        String.format(MakosResponseErrorCodes.HEDEF_BULUNAMADI
                                , hedefNo
                                , String.valueOf(hedefTipi.getHedefKodu())
                        )
                ));

                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, request.getKullaniciId(), saveDateTime);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);
                if(savedDMahkemeKararTalep == null){

                }
                dMahkemeKararTalep = savedDMahkemeKararTalep;
            }

            /*
            HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper.toHedeflerDetayTalep(iDHedefDetay, mahkemeKararTalepId, iliskiliHedef.getId(), request.getKullaniciId(), saveDateTime);
            //Yeni eklenen sutun
            hedeflerDetayTalep.setDetayMahkemeKararTalepId(dMahkemeKararTalep.getId());
            HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);
            if (savedHedeflerDetayTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_HEDEFLER_DETAY_KAYDETMEHATASI, hedefNo);
            }*/


            HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(iDHedefDetay, mahkemeKararTalepId, request.getKullaniciId(), saveDateTime);
            hedeflerTalepEntity.setDetayMahkemeKararTalepId(dMahkemeKararTalep.getId());
            //hedeflerTalepEntity.setKapatmaKararId(iliskiliMahkemeKarar.getId());

            HedeflerTalep savedHedeflerTalep = dbHedeflerTalepService.save(hedeflerTalepEntity);
            //Hedef Aidiyat Talepleri Kaydet
            if (iDHedefDetay.getHedefAidiyatKodlari() != null) {
                saveHedeflerTalepAidiyatListesi(iDHedefDetay.getHedefAidiyatKodlari()
                        , savedHedeflerTalep.getId()
                        , savedHedeflerTalep.getHedefNo()
                        , saveDateTime
                        , request.getKullaniciId());
            }

        });


    }

    //TODO : Sonlandirma karari icin hedef - aidiyat kodu gonderilmesi cok mantkli degil. Musteri ile konusulup netlestirilmeli
    private List<HedeflerAidiyatTalep> saveHedeflerTalepAidiyatListesi(List<String> sucKoduListesi, Long hedeflerTalepId, String hedefNo, LocalDateTime islemTarihi, Long kullaniciId) {
        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : sucKoduListesi) {

            HedeflerAidiyatTalep hedeflerAidiyatTalep = HedeflerAidiyatTalep.builder()
                    .hedefTalepId(hedeflerTalepId)
                    .aidiyatKod(aidiyatKodu)
                    .tarih(islemTarihi)
                    .kullaniciId(kullaniciId)
                    .build();

            HedeflerAidiyatTalep savedHedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.save(hedeflerAidiyatTalep);

            if (savedHedeflerAidiyatTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_HEDEFLER_AIDIYAT_KAYDETMEHATASI, hedefNo, aidiyatKodu);
            }

            result.add(savedHedeflerAidiyatTalep);
        }
        return result;
    }
}

