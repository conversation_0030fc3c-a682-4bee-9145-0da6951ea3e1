package iym.makos.domain.mktalep.requestprocessor.processor;


import iym.makos.model.MakosRequestResponse;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.IDHedefGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.IDHedefGuncellemeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MKProcessorFactory {

    private final Map<Class<? extends MkTalepRequest>, Map<Class<? extends MakosRequestResponse>, IMakosRequestProcessor<?, ?>>> processorsByRequest = new HashMap<>();

    @Autowired
    public MKProcessorFactory(List<IMakosRequestProcessor<?, ?>> requestProcessors) {
        for (IMakosRequestProcessor<?, ?> processor : requestProcessors) {
            processorsByRequest.computeIfAbsent(processor.getRelatedRequestType(), map -> new HashMap<>())
                    .put(processor.getRelatedResponseType(), processor);
        }
    }

    @SuppressWarnings("unchecked")
    public <T extends MkTalepRequest, R extends MakosRequestResponse> IMakosRequestProcessor<T, R> getProcessor(T request, Class<R> responseType) {
        Map<Class<? extends MakosRequestResponse>, IMakosRequestProcessor<?, ?>> requestProcessorMap = processorsByRequest.get(request.getClass());
        if (requestProcessorMap != null){
            IMakosRequestProcessor<?, ?> processor = requestProcessorMap.get(responseType);
            if (processor != null)
                return (IMakosRequestProcessor<T, R>) requestProcessorMap.get(responseType);
        }

        throw new RuntimeException("No processor found for <" + request.getClass().getSimpleName() + ", " + responseType.getSimpleName() + ">");
    }

    @SuppressWarnings("unchecked")
    public <T extends MkTalepRequest, R extends MakosRequestResponse> IMakosRequestProcessor<T, R> getProcessor(Class<T> requestType, Class<R> responseType) {
        Map<Class<? extends MakosRequestResponse>, IMakosRequestProcessor<?, ?>> requestProcessorMap = processorsByRequest.get(requestType);
        if (requestProcessorMap != null){
            IMakosRequestProcessor<?, ?> processor = requestProcessorMap.get(responseType);
            if (processor != null)
                return (IMakosRequestProcessor<T, R>) requestProcessorMap.get(responseType);
        }

        throw new RuntimeException("No processor found for <" + requestType.getSimpleName() + ", " + responseType.getSimpleName() + ">");
    }

}