package iym.makos.model.dto.mktalep.request.id.detay;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKararGuncellemeDetay;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class IDMahkemeKararGuncellemeRequestDetay implements IDRequestDetay {

    @NotNull
    @Valid
    @Size(min = 1)
    @Schema(description = "Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek yeni kod/il bilgileri")
    private List<MahkemeKararGuncellemeDetay> mahkemeKararGuncellemeDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if MahkemeBilgisiGuncellemeRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (mahkemeKararGuncellemeDetayListesi == null || mahkemeKararGuncellemeDetayListesi.isEmpty()) {
                validationResult.addFailedReason("MahkemeKararGuncelleme detay listesi boş olamaz.");
                return validationResult;
            }

            for (MahkemeKararGuncellemeDetay mahkemeKararGuncellemeDetay : mahkemeKararGuncellemeDetayListesi) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = mahkemeKararGuncellemeDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                }

            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

}

