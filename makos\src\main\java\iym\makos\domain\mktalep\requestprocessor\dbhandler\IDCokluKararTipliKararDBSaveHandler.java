package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.KararTuru;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.id.*;
import iym.makos.model.dto.mktalep.request.id.detay.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IDCokluKararTipliKararDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDCokluKararTipliKararRequest> {

    private final IDYeniKararDBSaveHandler idYeniKararDBSaveHandler;
    private final IDUzatmaKarariDBSaveHandler idUzatmaKarariDBSaveHandler;
    private final IDSonlandirmaKarariDBSaveHandler idSonlandirmaKarariDBSaveHandler;
    private final IDAidiyatBilgisiGuncellemeDBSaveHandler idAidiyatBilgisiGuncellemeDBSaveHandler;
    private final IDHedefGuncellemeDBSaveHandler idHedefGuncellemeDBSaveHandler;

    @Autowired
    public IDCokluKararTipliKararDBSaveHandler(
            IDYeniKararDBSaveHandler idYeniKararDBSaveHandler
            , IDUzatmaKarariDBSaveHandler uzatmaKarariDBSaveHandler
            , IDSonlandirmaKarariDBSaveHandler idSonlandirmaKarariDBSaveHandler
            , IDAidiyatBilgisiGuncellemeDBSaveHandler idAidiyatBilgisiGuncellemeDBSaveHandler
            , IDHedefGuncellemeDBSaveHandler idHedefGuncellemeDBSaveHandler) {
        this.idYeniKararDBSaveHandler = idYeniKararDBSaveHandler;
        this.idUzatmaKarariDBSaveHandler = uzatmaKarariDBSaveHandler;
        this.idSonlandirmaKarariDBSaveHandler = idSonlandirmaKarariDBSaveHandler;
        this.idAidiyatBilgisiGuncellemeDBSaveHandler = idAidiyatBilgisiGuncellemeDBSaveHandler;
        this.idHedefGuncellemeDBSaveHandler = idHedefGuncellemeDBSaveHandler;

    }

    @Override
    public void saveRequestSpecificDetails(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> request) {

        validateRequest(request);

        IDCokluKararTipliKararRequest kararRequest = request.getKararRequest();

        if (kararRequest.getYeniKararRequestDetay() != null) {
            saveYeniKararRequest(mahkemeKararTalepIdWithEvrakId, request);
        }

        if (kararRequest.getUzatmaKarariRequestDetay() != null) {
            saveUzatmaKarariRequest(mahkemeKararTalepIdWithEvrakId, request);
        }

        if (kararRequest.getSonlandirmaKarariRequestDetay() != null) {
            saveSonlandirmaKarariRequest(mahkemeKararTalepIdWithEvrakId, request);
        }

        if (kararRequest.getAidiyatBilgisiGuncellemeRequestDetay() != null) {
            saveAidiyatBilgisiGuncellemeRequest(mahkemeKararTalepIdWithEvrakId, request);
        }

        if (kararRequest.getHedefGuncellemeRequestDetay() != null) {
            saveHedefGuncellemeRequest(mahkemeKararTalepIdWithEvrakId, request);
        }

    }

    private void saveYeniKararRequest(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> request) {
        IDCokluKararTipliKararRequest kararRequest = request.getKararRequest();

        IDYeniKararRequestDetay requestDetay = kararRequest.getYeniKararRequestDetay();
        IDYeniKararRequest yeniKararRequest = IDYeniKararRequest.builder()
                .yeniKararRequestDetay(requestDetay)
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR)
                .build();
        MkTalepDbSaveRequest<IDYeniKararRequest> saveRequest = MkTalepDbSaveRequest.<IDYeniKararRequest>builder()
                .kullaniciId(request.getKullaniciId())
                .kararRequest(yeniKararRequest)
                .build();

        idYeniKararDBSaveHandler.saveRequestSpecificDetails(mahkemeKararTalepIdWithEvrakId, saveRequest);
    }

    private void saveUzatmaKarariRequest(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> request) {
        IDCokluKararTipliKararRequest kararRequest = request.getKararRequest();

        IDUzatmaKarariRequestDetay requestDetay = kararRequest.getUzatmaKarariRequestDetay();
        IDUzatmaKarariRequest uzatmaKarariRequest = IDUzatmaKarariRequest.builder()
                .uzatmaKarariRequestDetay(requestDetay)
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                .build();
        MkTalepDbSaveRequest<IDUzatmaKarariRequest> saveRequest = MkTalepDbSaveRequest.<IDUzatmaKarariRequest>builder()
                .kullaniciId(request.getKullaniciId())
                .kararRequest(uzatmaKarariRequest)
                .build();

        idUzatmaKarariDBSaveHandler.saveRequestSpecificDetails(mahkemeKararTalepIdWithEvrakId, saveRequest);
    }

    private void saveSonlandirmaKarariRequest(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> request) {
        IDCokluKararTipliKararRequest kararRequest = request.getKararRequest();

        IDSonlandirmaKarariRequestDetay requestDetay = kararRequest.getSonlandirmaKarariRequestDetay();
        IDSonlandirmaKarariRequest sonlandirmaKarariRequest = IDSonlandirmaKarariRequest.builder()
                .sonlandirmaKarariRequestDetay(requestDetay)
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .build();
        MkTalepDbSaveRequest<IDSonlandirmaKarariRequest> saveRequest = MkTalepDbSaveRequest.<IDSonlandirmaKarariRequest>builder()
                .kullaniciId(request.getKullaniciId())
                .kararRequest(sonlandirmaKarariRequest)
                .build();

        idSonlandirmaKarariDBSaveHandler.saveRequestSpecificDetails(mahkemeKararTalepIdWithEvrakId, saveRequest);
    }

    private void saveAidiyatBilgisiGuncellemeRequest(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> request) {
        IDCokluKararTipliKararRequest kararRequest = request.getKararRequest();

        IDAidiyatBilgisiGuncellemeRequestDetay requestDetay = kararRequest.getAidiyatBilgisiGuncellemeRequestDetay();
        IDAidiyatBilgisiGuncellemeRequest aidiyatBilgisiGuncellemeRequest = IDAidiyatBilgisiGuncellemeRequest.builder()
                .aidiyatBilgisiGuncellemeRequestDetay(requestDetay)
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME)
                .build();
        MkTalepDbSaveRequest<IDAidiyatBilgisiGuncellemeRequest> saveRequest = MkTalepDbSaveRequest.<IDAidiyatBilgisiGuncellemeRequest>builder()
                .kullaniciId(request.getKullaniciId())
                .kararRequest(aidiyatBilgisiGuncellemeRequest)
                .build();

        idAidiyatBilgisiGuncellemeDBSaveHandler.saveRequestSpecificDetails(mahkemeKararTalepIdWithEvrakId, saveRequest);
    }

    private void saveHedefGuncellemeRequest(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> request) {
        IDCokluKararTipliKararRequest kararRequest = request.getKararRequest();

        IDHedefGuncellemeRequestDetay requestDetay = kararRequest.getHedefGuncellemeRequestDetay();
        IDHedefGuncellemeRequest hedefGuncellemeRequest = IDHedefGuncellemeRequest.builder()
                .hedefGuncellemeRequestDetay(requestDetay)
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME)
                .build();
        MkTalepDbSaveRequest<IDHedefGuncellemeRequest> saveRequest = MkTalepDbSaveRequest.<IDHedefGuncellemeRequest>builder()
                .kullaniciId(request.getKullaniciId())
                .kararRequest(hedefGuncellemeRequest)
                .build();

        idHedefGuncellemeDBSaveHandler.saveRequestSpecificDetails(mahkemeKararTalepIdWithEvrakId, saveRequest);
    }

    @Override
    protected void validateRequest(MkTalepDbSaveRequest<IDCokluKararTipliKararRequest> request) {

        super.validateRequest(request);

        IDCokluKararTipliKararRequest kararRequest = request.getKararRequest();
        if (kararRequest.getYeniKararRequestDetay() == null &&
                kararRequest.getUzatmaKarariRequestDetay() == null &&
                kararRequest.getSonlandirmaKarariRequestDetay() == null &&
                kararRequest.getAidiyatBilgisiGuncellemeRequestDetay() == null &&
                kararRequest.getHedefGuncellemeRequestDetay() == null
        ) {
            String errorStr = CommonUtils.getFormattedString(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "request en az bir adet karar türü içermelidir");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
    }
}

