package iym.makos.model.dto.mktalep.request.genelevrak;

import iym.makos.model.MakosRequestResponse;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepSorguView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class GenelEvrakSorgulamaResponse extends MakosRequestResponse {
    private List<MahkemeKararTalepSorguView> mahkemeKararTalepSorguViewListesi;
} 