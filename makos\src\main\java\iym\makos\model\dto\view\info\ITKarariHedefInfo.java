package iym.makos.model.dto.view.info;

import iym.common.enums.HedefTip;
import iym.common.enums.SureTip;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class ITKarariHedefInfo {

  private String hedefNo;

  private HedefTip hedefTip;

  private String hedefAdi;

  private String hedefSoyadi;

}

