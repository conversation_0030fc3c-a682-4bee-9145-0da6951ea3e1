package iym.makos.domain.mkislem.enrich.service;


import iym.common.enums.KararTuru;
import iym.common.model.api.Response;
import iym.makos.domain.mkislem.enrich.MKIslemEnricher;
import iym.makos.model.dto.view.MahkemeKarariInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MkIslemEnricherFactory {

    private final Map<KararTuru, MKIslemEnricher> enricherMap = new HashMap<>();

    @Autowired
    public MkIslemEnricherFactory(List<MKIslemEnricher> enricherList) {
        for (MKIslemEnricher enricher : enricherList) {
            enricherMap.put(enricher.getSupportedKararTuru(), enricher);
        }
    }

    public Response<String> enrich(MahkemeKarariInfo mahkemeKarariInfo) {
        MKIslemEnricher enricher = enricherMap.get(mahkemeKarariInfo.getKararTuru());
        return enricher.enrich(mahkemeKarariInfo);
    }

    public Response<String> enrich(MahkemeKarariInfo mahkemeKarariInfo, KararTuru kararTuru) {
        MKIslemEnricher enricher = enricherMap.get(kararTuru);
        return enricher.enrich(mahkemeKarariInfo);
    }
}
