package iym.backend.kullanicigrupyetki.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.shared.entity.BaseEntity;
import iym.backend.yetki.entity.Yetki;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "kullanici_grup_yetkiler")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KullaniciGrupYetki extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "kullanici_grup_id", nullable = false)
    private KullaniciGrup kullaniciGrup;

    @ManyToOne
    @JoinColumn(name = "yetki_id", nullable = false)
    private Yetki yetki;

}

