package iym.makos.domain.mktaleprequest.dbhandler;

import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.GenelEvrakDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.genelevrak.GenelEvrakRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

/**
 * Unit tests for GenelEvrakDBSaveHandler.
 * <p>
 * Tests the DB handler implementation for general evrak save operations.
 * Verifies database save operations and error handling scenarios.
 *
 * <AUTHOR> Team
 */
@DisplayName("GenelEvrakDBSaveHandler Unit Tests")
class GenelEvrakDBSaveHandlerTest extends BaseDomainUnitTest {

    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @InjectMocks
    private GenelEvrakDBSaveHandler dbSaveHandler;

    private MahkemeKararTalepIdWithEvrakId talepIdWithEvrakId;
    private GenelEvrakRequest request;
    private MkTalepDbSaveRequest<GenelEvrakRequest> dbSaveRequest;
    private LocalDateTime kayitTarihi;
    private Long kullaniciId;


    @BeforeEach
    void setUp() {
        talepIdWithEvrakId = new MahkemeKararTalepIdWithEvrakId(1L, 2L);
        request = new GenelEvrakRequest();
        dbSaveRequest = MkTalepDbSaveRequest.<GenelEvrakRequest>builder()
                .kararRequest(request)
                .kullaniciId(kullaniciId)
                .build();
        kayitTarihi = LocalDateTime.now();
        kullaniciId = 42L;


        talepIdWithEvrakId = createTestMahkemeKararTalepIdWithEvrakId();

    }

    @Test
    void saveRequestSpecificDetails_nullTalep_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(null, dbSaveRequest))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("mahkemeKarar/EvrakId boş olamaz");
    }

    @Test
    void saveRequestSpecificInfo_nullRequest_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, null))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("request boş olamaz");
    }

    @Test
    void saveRequestSpecificInfo_nullKararRequest_throwsException() {
        MkTalepDbSaveRequest<GenelEvrakRequest> dbSaveRequest = MkTalepDbSaveRequest.<GenelEvrakRequest>builder()
                .kararRequest(null)
                .kullaniciId(kullaniciId)
                .build();
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, dbSaveRequest))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("request boş olamaz");
    }

    @Test
    void saveRequestSpecificDetails_nullKullaniciId_throwsException() {
        MkTalepDbSaveRequest<GenelEvrakRequest> dbSaveRequest = MkTalepDbSaveRequest.<GenelEvrakRequest>builder()
                .kararRequest(request)
                .kullaniciId(null)
                .build();
        assertThatThrownBy(() -> dbSaveHandler.saveRequestSpecificDetails(talepIdWithEvrakId, dbSaveRequest))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kullaniciId boş olamaz");
    }




}
