package iym.makos.domain.mktalep.requestprocessor.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.DbSucTipiService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import iym.makos.model.dto.mktalep.request.id.detay.IDYeniKararRequestDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Optional;

@Service
@Slf4j
public class IDYeniKararValidator extends MahkemeKararRequestValidatorBase<IDYeniKararRequest> {

    public static final LocalDate OHAL_DATE = LocalDate.parse("2018-07-19");
    private final DbSucTipiService dbSucTipiService;

    @Autowired
    public IDYeniKararValidator(DbSucTipiService dbSucTipiService) {
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    protected ValidationResult doValidate(IDYeniKararRequest request) {

        try {
            log.info("IDYeniKararValidator Validation");

            ValidationResult validationResult = new ValidationResult(true);

            //null kontrolu yapmaya gerek yok cunku request'te  @NotNull annotation var.
            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);
            MahkemeKararTip kararTipi = request.getMahkemeKararBilgisi().getMahkemeKararTipi();

            //Ohal Kontrolu
            if (LocalDate.now().isAfter(OHAL_DATE) && kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)) {
                validationResult.addFailedReason("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
            }

            IDYeniKararRequestDetay kararRequestDetay = request.getYeniKararRequestDetay();

            if (kararRequestDetay == null) {
                validationResult.addFailedReason("IDYeniKararRequestDetay boş olamaz");
                return validationResult;
            }

            CommonUtils.safeList(kararRequestDetay.getHedefDetayListesi())
                    .forEach(hedefler -> {
                        if( hedefler.getUzatmaSayisi() != null && hedefler.getUzatmaSayisi() > 0) {
                            validationResult.addFailedReason("Yeni kararda UzatmaSayısı belirtilemez");
                        }
                    });


            CommonUtils.safeList(kararRequestDetay.getMahkemeSucTipiKodlari())
                    .forEach(sucTipiKodu -> {
                        Optional<SucTipi> sucTipiOpt = dbSucTipiService.findBySucTipiKodu(sucTipiKodu);
                        if (sucTipiOpt.isEmpty()) {
                            validationResult.addFailedReason(String.format( "'%s' kodu ile eşleşen bir suç tipi mevcut değil.", sucTipiKodu));
                        }
                    });

            CommonUtils.safeList(kararRequestDetay.getMahkemeAidiyatKodlari())
                    .forEach(aidiyatKod -> {
                        String uyariMesaj = CommonUtils.aidiyatEklemeUyarisi(aidiyatKod, kararTipi, evrakKurum);
                        if (!CommonUtils.isNullOrEmpty(uyariMesaj)) {
                            validationResult.addFailedReason(uyariMesaj);
                        }
                    });

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }

}

