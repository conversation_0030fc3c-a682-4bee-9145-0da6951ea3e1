package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.dto.mktalep.dbsave.MkTalepDbSaveRequest;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.GenericTypeResolver;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
public abstract class MahkemeKararRequestDbSaveHandlerBase<T extends MkTalepRequest> implements MahkemeKararDBSaveHandler<T> {

    protected MahkemeKararRequestCommonDbSaveHandler commonDbSaveHandler;
    protected DbMahkemeKararTalepService dbMahkemeKararTalepService;
    protected KararRequestMapper kararRequestMapper;

    @Autowired
    public final void setMahkemeKararRequestDbSaveHandlerBase(MahkemeKararRequestCommonDbSaveHandler commonDbSaveHandler) {
        this.commonDbSaveHandler = commonDbSaveHandler;
    }

    @Autowired
    public final void setDbMahkemeKararTalepService(DbMahkemeKararTalepService dbMahkemeKararTalepService) {
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
    }

    @Autowired
    public final void setKararRequestMapper(KararRequestMapper kararRequestMapper) {
        this.kararRequestMapper = kararRequestMapper;
    }

    @Override
    @Transactional
    public MahkemeKararTalepIdWithEvrakId kaydet(MkTalepDbSaveRequest<T> request) {

        validateRequest(request);

        Long savedEvrakId = this.saveEvrakBilgileri(request);
        if (savedEvrakId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getKararRequest().getId(), MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        Long mahkemeKararTalepId = this.saveMahkemeKararTalep(savedEvrakId, request);
        if (mahkemeKararTalepId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getKararRequest().getId(), MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = MahkemeKararTalepIdWithEvrakId.builder()
                .evrakId(savedEvrakId)
                .mahkemeKararTalepId(mahkemeKararTalepId)
                .build();

        this.saveRequestSpecificDetails(mahkemeKararTalepIdWithEvrakId, request);

        return mahkemeKararTalepIdWithEvrakId;
    }

    @Override
    public Long saveEvrakBilgileri(MkTalepDbSaveRequest<T> request) {
        return commonDbSaveHandler.saveEvrakBilgileri(request);
    }

    @Override
    public Long saveMahkemeKararTalep(Long evrakId, MkTalepDbSaveRequest<T> request){

        validateRequest(request);

        T kararRequest = request.getKararRequest();

        LocalDateTime saveDateTime = LocalDateTime.now();
        MahkemeKararTalep talep = kararRequestMapper.toMahkemeKararTalep(
                kararRequest.getMahkemeKararBilgisi(),
                evrakId,
                request.getKullaniciId(),
                saveDateTime
        );

        MahkemeKararTalep savedMahkemeKararTalep = dbMahkemeKararTalepService.save(talep);
        if (savedMahkemeKararTalep == null || savedMahkemeKararTalep.getId() == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.MKTALEP_KAYIT_HATASI);
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        return savedMahkemeKararTalep.getId();
    }

    protected void validateRequest(MkTalepDbSaveRequest<T> request) {

        if (request == null) {
            String errorStr = CommonUtils.getFormattedString(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "request boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        ValidationResult validate = request.validate();
        if (!validate.isValid()) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getKararRequest().getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "Hatalı request: " + String.join(", ", validate.getReasons()));
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
    }

    @SuppressWarnings("unchecked")
    public Class<T> getRelatedRequestType() {
        return (Class<T>) GenericTypeResolver.resolveTypeArgument(
                this.getClass(),
                MahkemeKararDBSaveHandler.class
        );
    }
}

