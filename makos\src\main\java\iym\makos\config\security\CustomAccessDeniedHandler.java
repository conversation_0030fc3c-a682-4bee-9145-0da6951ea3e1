package iym.makos.config.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.util.EnvironmentUtil;
import iym.common.util.JsonUtils;
import iym.common.model.api.ProblemDetailBuilder;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ProblemDetail;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    private final EnvironmentUtil environmentUtil;

    public CustomAccessDeniedHandler(EnvironmentUtil environmentUtil) {
        this.environmentUtil = environmentUtil;
    }

    @Override
    public void handle(HttpServletRequest request,
                       HttpServletResponse response,
                       AccessDeniedException accessDeniedException)
            throws IOException, ServletException {

        response.setStatus(HttpServletResponse.SC_FORBIDDEN); // 403
        response.setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);

        String message = "Access Denied: You do not have the required role to access this resource";

        // Create ProblemDetail for consistent error response format
        ProblemDetail problemDetail = ProblemDetailBuilder.authorizationError(message);
        problemDetail.setProperty("path", request.getServletPath());

        // Always log full details for debugging
        log.error("Access Denied at filter level. uri:{}, user:{}, error:{}",
                request.getServletPath(),
                getCurrentUser(),
                accessDeniedException.getMessage(),
                accessDeniedException);

        final ObjectMapper mapper = JsonUtils.getMapper();
        mapper.writeValue(response.getOutputStream(), problemDetail);
        response.getOutputStream().flush();
    }

    /**
     * Get current authenticated user for logging purposes
     */
    private String getCurrentUser() {
        try {
            return org.springframework.security.core.context.SecurityContextHolder
                    .getContext()
                    .getAuthentication()
                    .getName();
        } catch (Exception e) {
            return "anonymous";
        }
    }
}
